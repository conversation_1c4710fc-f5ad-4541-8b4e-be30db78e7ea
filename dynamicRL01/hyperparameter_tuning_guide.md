# PPO Hyperparameter Tuning Guide

## Overview
This guide explains all the hyperparameters that can be tuned to improve your PPO agent's performance in the TSN GCL environment.

## Currently Implemented Hyperparameters

### 1. Learning Rate Parameters
- **learning_rate**: `1e-5` to `1e-3` (log scale)
  - Controls how fast the agent learns
  - Lower values: more stable but slower learning
  - Higher values: faster learning but potentially unstable

### 2. Network Architecture Parameters
- **n_layers**: `1` to `4` layers
  - Number of hidden layers in both actor and critic networks
  - More layers can capture complex patterns but may overfit

- **layer_size**: `[32, 64, 128, 256]`
  - Number of neurons in each hidden layer
  - Larger networks can learn more complex policies but require more data

- **activation_fn**: `['relu', 'tanh', 'elu']`
  - Activation function for neural networks
  - ReLU: most common, good for most cases
  - Tanh: bounded output, good for continuous control
  - ELU: smooth, can help with gradient flow

### 3. PPO-Specific Parameters
- **entropy_regularization**: `0.001` to `0.1` (log scale)
  - Encourages exploration by adding randomness to actions
  - Higher values: more exploration, less exploitation
  - Lower values: more exploitation, less exploration

- **importance_ratio_clipping**: `0.1` to `0.3`
  - Prevents large policy updates that could destabilize training
  - Lower values: more conservative updates
  - Higher values: allows larger policy changes

- **num_epochs**: `2` to `8`
  - Number of times to update the policy with each batch of data
  - More epochs: better data utilization but risk of overfitting
  - Fewer epochs: less overfitting but potentially slower learning

### 4. Generalized Advantage Estimation (GAE) Parameters
- **lambda_value**: `0.9` to `0.99`
  - Controls bias-variance tradeoff in advantage estimation
  - Higher values: lower bias but higher variance
  - Lower values: higher bias but lower variance

- **discount_factor**: `0.95` to `0.999`
  - How much future rewards are valued compared to immediate rewards
  - Higher values: agent considers long-term consequences more
  - Lower values: agent focuses on immediate rewards

### 5. Training Parameters
- **collect_steps_per_iteration**: `[50, 100, 150, 200]`
  - Number of environment steps to collect before each training update
  - More steps: better data diversity but slower updates
  - Fewer steps: faster updates but potentially less diverse data

- **num_iterations**: `10` to `25`
  - Total number of training iterations
  - More iterations: longer training, potentially better performance
  - Fewer iterations: faster training, good for hyperparameter search

## Additional Parameters You Could Add

### 6. Optimizer Parameters
- **optimizer_type**: `['adam', 'rmsprop', 'sgd']`
- **beta1**: `0.8` to `0.95` (for Adam)
- **beta2**: `0.9` to `0.999` (for Adam)
- **epsilon**: `1e-8` to `1e-6` (for Adam)

### 7. Network Initialization
- **kernel_initializer**: `['variance_scaling', 'glorot_uniform', 'he_normal']`
- **initialization_scale**: `0.5` to `2.0`

### 8. Gradient Clipping
- **gradient_clipping**: `0.1` to `2.0`
- **clipnorm_value**: `0.5` to `2.0`

### 9. Replay Buffer Parameters (for Prioritized Experience Replay)
- **buffer_size**: `[5000, 10000, 20000]`
- **alpha**: `0.4` to `0.8` (prioritization strength)
- **beta**: `0.2` to `0.6` (importance sampling correction)
- **epsilon**: `1e-8` to `1e-4` (small constant for numerical stability)

### 10. Learning Rate Schedule
- **lr_decay_rate**: `0.95` to `0.99`
- **lr_decay_steps**: `100` to `1000`
- **min_learning_rate**: `1e-6` to `1e-4`

### 11. Value Function Parameters
- **value_function_loss_coefficient**: `0.25` to `1.0`
- **normalize_advantages**: `[True, False]`
- **advantage_clipping**: `[-10.0, 10.0]`

## Hyperparameter Interaction Effects

### Important Combinations to Consider:
1. **Learning Rate + Network Size**: Larger networks often need smaller learning rates
2. **Entropy + Clipping**: Higher entropy may require tighter clipping
3. **GAE Lambda + Discount**: Both affect temporal credit assignment
4. **Epochs + Batch Size**: More epochs with smaller batches can improve stability

## Tuning Strategy Recommendations

### Phase 1: Core Parameters
Start with the most impactful parameters:
1. Learning rate
2. Network architecture (layers, size)
3. Entropy regularization
4. Clipping ratio

### Phase 2: Training Dynamics
Once core parameters are stable:
1. GAE lambda and discount factor
2. Number of epochs
3. Training iterations and steps

### Phase 3: Advanced Optimization
For fine-tuning:
1. Optimizer parameters
2. Initialization schemes
3. Gradient clipping
4. Learning rate schedules

## Performance Monitoring

### Key Metrics to Track:
- **Average Return**: Primary optimization target
- **Policy Loss**: Should decrease over time
- **Value Loss**: Should stabilize
- **Entropy**: Should decrease gradually
- **KL Divergence**: Should stay within reasonable bounds
- **Gradient Norms**: Should not explode

### Warning Signs:
- **Exploding gradients**: Reduce learning rate or increase clipping
- **Vanishing gradients**: Increase learning rate or change initialization
- **Policy collapse**: Increase entropy regularization
- **Slow convergence**: Increase learning rate or network size
- **Overfitting**: Reduce network size or increase regularization

## Implementation Notes

The enhanced `optuna_objective` function now tunes 11 different hyperparameters:
1. learning_rate
2. n_layers
3. layer_size
4. activation_fn
5. entropy_regularization
6. importance_ratio_clipping
7. num_epochs
8. lambda_value
9. discount_factor
10. collect_steps_per_iteration
11. num_iterations

This provides a much more comprehensive search space for finding optimal hyperparameters for your specific TSN GCL environment.
