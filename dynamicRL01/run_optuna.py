import optuna
import logging
import os
import sys
import time
from revamped import optuna_objective

# Configure logging
logging.basicConfig(level=logging.INFO, 
                   filename='optuna_study.log',
                   format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                   filemode='a')

def main():
    """Run Optuna hyperparameter optimization for TSNGCLEnvironment."""
    print("Starting Optuna hyperparameter optimization for TSNGCLEnvironment")
    logging.info("Starting Optuna hyperparameter optimization for TSNGCLEnvironment")
    
    # Create a new study or load an existing one
    study_name = "tsngcl_optimization"
    storage_name = "sqlite:///optuna_study.db"
    
    try:
        # Create a new study or load an existing one
        study = optuna.create_study(
            study_name=study_name,
            storage=storage_name,
            load_if_exists=True,
            direction="maximize"  # We want to maximize the return
        )
        
        print(f"Study '{study_name}' created or loaded successfully")
        logging.info(f"Study '{study_name}' created or loaded successfully")
        
        # Print study statistics if it already exists
        if len(study.trials) > 0:
            print(f"Study already has {len(study.trials)} trials")
            logging.info(f"Study already has {len(study.trials)} trials")
            
            # Print best trial so far
            print(f"Best trial so far: #{study.best_trial.number}")
            print(f"  Value: {study.best_trial.value}")
            print(f"  Params: {study.best_trial.params}")
            logging.info(f"Best trial so far: #{study.best_trial.number}")
            logging.info(f"  Value: {study.best_trial.value}")
            logging.info(f"  Params: {study.best_trial.params}")
        
        # Set the number of trials to run
        n_trials = 20  # Adjust based on your computational resources and time constraints
        
        # Run the optimization
        print(f"Starting optimization with {n_trials} trials")
        logging.info(f"Starting optimization with {n_trials} trials")
        
        study.optimize(optuna_objective, n_trials=n_trials, timeout=None)
        
        # Print optimization results
        print("Optimization completed successfully")
        print(f"Best trial: #{study.best_trial.number}")
        print(f"  Value: {study.best_trial.value}")
        print(f"  Params: {study.best_trial.params}")
        
        logging.info("Optimization completed successfully")
        logging.info(f"Best trial: #{study.best_trial.number}")
        logging.info(f"  Value: {study.best_trial.value}")
        logging.info(f"  Params: {study.best_trial.params}")
        
        # Save the best parameters to a file for easy reference
        with open("best_params.txt", "w") as f:
            f.write(f"Best trial: #{study.best_trial.number}\n")
            f.write(f"Value: {study.best_trial.value}\n")
            f.write("Parameters:\n")
            for param_name, param_value in study.best_trial.params.items():
                f.write(f"  {param_name}: {param_value}\n")
        
        print("Best parameters saved to best_params.txt")
        logging.info("Best parameters saved to best_params.txt")
        
    except Exception as e:
        print(f"Error during optimization: {e}")
        logging.error(f"Error during optimization: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
