#!/usr/bin/env python3
"""
Loss Diagnostic Tool for PPO Agent

This script helps diagnose why the PPO agent's loss remains high by analyzing:
1. Reward function behavior
2. Data quality and distribution
3. Network architecture issues
4. Training dynamics
5. Hyperparameter settings
"""

import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
import json
import logging
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LossDiagnostic:
    def __init__(self, env, agent, tf_env):
        self.env = env
        self.agent = agent
        self.tf_env = tf_env
        self.reward_history = []
        self.observation_history = []
        self.action_history = []
        self.loss_history = []
        
    def analyze_reward_function(self, num_samples=100):
        """Analyze the reward function behavior."""
        print("=== REWARD FUNCTION ANALYSIS ===")
        
        rewards = []
        observations = []
        
        # Collect samples
        for i in range(num_samples):
            if not self.env.data_active:
                print("Waiting for data...")
                time.sleep(1)
                continue
                
            obs = self.env._get_flattened_observation()
            reward = self.env._calculate_reward(obs)
            
            rewards.append(reward)
            observations.append(obs)
            
        if not rewards:
            print("❌ No rewards collected - data stream inactive")
            return
            
        rewards = np.array(rewards)
        observations = np.array(observations)
        
        print(f"📊 Reward Statistics:")
        print(f"   Mean: {np.mean(rewards):.4f}")
        print(f"   Std:  {np.std(rewards):.4f}")
        print(f"   Min:  {np.min(rewards):.4f}")
        print(f"   Max:  {np.max(rewards):.4f}")
        print(f"   Range: {np.max(rewards) - np.min(rewards):.4f}")
        
        # Check for reward issues
        if np.std(rewards) < 0.1:
            print("⚠️  WARNING: Very low reward variance - agent may not learn effectively")
        if np.mean(rewards) < 0:
            print("⚠️  WARNING: Negative mean reward - consider reward shaping")
        if np.max(rewards) - np.min(rewards) > 20:
            print("⚠️  WARNING: Very large reward range - may cause training instability")
            
        return rewards, observations
    
    def analyze_data_quality(self):
        """Analyze the quality of data from OMNeT++."""
        print("\n=== DATA QUALITY ANALYSIS ===")
        
        if not self.env.data_active:
            print("❌ Data stream is inactive")
            return False
            
        print(f"✅ Data stream is active")
        print(f"📡 Expected queues: {self.env.expected_queues}")
        print(f"🔢 Number of queues: {self.env.num_queues}")
        
        # Check queue states
        with self.env.lock:
            for queue_id in self.env.expected_queues:
                if queue_id in self.env.queue_states:
                    state = self.env.queue_states[queue_id]
                    print(f"   Queue {queue_id}: {len(state)} metrics")
                    if state:
                        for key, value in state.items():
                            print(f"     {key}: {value}")
                else:
                    print(f"❌ Queue {queue_id} not found in queue_states")
                    
        return True
    
    def analyze_network_gradients(self):
        """Analyze network gradients for vanishing/exploding gradient issues."""
        print("\n=== GRADIENT ANALYSIS ===")
        
        try:
            # Get a sample batch
            time_step = self.tf_env.reset()
            action_step = self.agent.policy.action(time_step)
            next_time_step = self.tf_env.step(action_step.action)
            
            # Create a simple trajectory
            trajectory = tf.nest.map_structure(
                lambda x: tf.expand_dims(x, 0),
                (time_step, action_step, next_time_step)
            )
            
            # Compute gradients
            with tf.GradientTape() as tape:
                loss_info = self.agent.train(trajectory)
                
            # Get trainable variables
            trainable_vars = self.agent.trainable_variables
            gradients = tape.gradient(loss_info.loss, trainable_vars)
            
            # Analyze gradients
            grad_norms = []
            for grad in gradients:
                if grad is not None:
                    grad_norms.append(tf.norm(grad).numpy())
                    
            if grad_norms:
                print(f"📊 Gradient Statistics:")
                print(f"   Mean norm: {np.mean(grad_norms):.6f}")
                print(f"   Max norm:  {np.max(grad_norms):.6f}")
                print(f"   Min norm:  {np.min(grad_norms):.6f}")
                
                if np.max(grad_norms) > 10.0:
                    print("⚠️  WARNING: Large gradients detected - may cause instability")
                if np.mean(grad_norms) < 1e-6:
                    print("⚠️  WARNING: Very small gradients - may indicate vanishing gradients")
            else:
                print("❌ No gradients computed")
                
        except Exception as e:
            print(f"❌ Error analyzing gradients: {e}")
    
    def analyze_action_distribution(self, num_samples=50):
        """Analyze the distribution of actions taken by the agent."""
        print("\n=== ACTION DISTRIBUTION ANALYSIS ===")
        
        actions = []
        
        for i in range(num_samples):
            if not self.env.data_active:
                time.sleep(0.1)
                continue
                
            time_step = self.tf_env.reset()
            action_step = self.agent.policy.action(time_step)
            actions.append(action_step.action.numpy())
            
        if not actions:
            print("❌ No actions collected")
            return
            
        actions = np.array(actions)
        
        print(f"📊 Action Statistics:")
        print(f"   Shape: {actions.shape}")
        print(f"   Mean: {np.mean(actions, axis=0)}")
        print(f"   Std:  {np.std(actions, axis=0)}")
        print(f"   Min:  {np.min(actions, axis=0)}")
        print(f"   Max:  {np.max(actions, axis=0)}")
        
        # Check for action saturation
        action_spec = self.tf_env.action_spec()
        min_vals = action_spec.minimum
        max_vals = action_spec.maximum
        
        for i in range(actions.shape[1]):
            min_ratio = np.mean(actions[:, i] <= min_vals[i] + 0.1)
            max_ratio = np.mean(actions[:, i] >= max_vals[i] - 0.1)
            
            if min_ratio > 0.8:
                print(f"⚠️  WARNING: Action {i} saturated at minimum ({min_ratio:.1%} of time)")
            if max_ratio > 0.8:
                print(f"⚠️  WARNING: Action {i} saturated at maximum ({max_ratio:.1%} of time)")
                
        return actions
    
    def analyze_loss_components(self):
        """Analyze individual loss components."""
        print("\n=== LOSS COMPONENT ANALYSIS ===")
        
        try:
            # Get a training batch
            time_step = self.tf_env.reset()
            action_step = self.agent.policy.action(time_step)
            next_time_step = self.tf_env.step(action_step.action)
            
            # Train and get loss info
            loss_info = self.agent.train(tf.nest.map_structure(
                lambda x: tf.expand_dims(x, 0),
                (time_step, action_step, next_time_step)
            ))
            
            print(f"📊 Loss Components:")
            print(f"   Total Loss: {loss_info.loss:.6f}")
            
            # Try to access individual loss components if available
            if hasattr(loss_info, 'extra'):
                for key, value in loss_info.extra.items():
                    if isinstance(value, tf.Tensor):
                        print(f"   {key}: {value.numpy():.6f}")
                        
        except Exception as e:
            print(f"❌ Error analyzing loss components: {e}")
    
    def run_full_diagnostic(self):
        """Run all diagnostic tests."""
        print("🔍 STARTING COMPREHENSIVE LOSS DIAGNOSTIC")
        print("=" * 50)
        
        # 1. Data quality check
        data_ok = self.analyze_data_quality()
        if not data_ok:
            print("\n❌ CRITICAL: Data stream issues detected. Fix data connection first.")
            return
        
        # 2. Reward function analysis
        self.analyze_reward_function()
        
        # 3. Action distribution analysis
        self.analyze_action_distribution()
        
        # 4. Gradient analysis
        self.analyze_network_gradients()
        
        # 5. Loss component analysis
        self.analyze_loss_components()
        
        print("\n" + "=" * 50)
        print("🔍 DIAGNOSTIC COMPLETE")
        
        # Provide recommendations
        self.provide_recommendations()
    
    def provide_recommendations(self):
        """Provide recommendations based on diagnostic results."""
        print("\n=== RECOMMENDATIONS ===")
        
        recommendations = [
            "1. 🎯 Reward Shaping: Consider normalizing rewards to [-1, 1] range",
            "2. 📊 Data Preprocessing: Add observation normalization",
            "3. 🧠 Network Architecture: Try different layer sizes or activation functions",
            "4. 📈 Learning Rate: Consider adaptive learning rate scheduling",
            "5. 🔄 Training Stability: Add gradient clipping if gradients are large",
            "6. 📝 Logging: Enable detailed loss component logging",
            "7. 🎲 Exploration: Increase entropy regularization if actions are saturated",
            "8. 🏃 Training Duration: Allow more training iterations for convergence"
        ]
        
        for rec in recommendations:
            print(rec)

def main():
    """Main diagnostic function."""
    from revamped_new import TSNGCLEnvironment, create_ppo_agent
    
    print("Initializing environment for diagnostic...")
    
    # Create environment
    env = TSNGCLEnvironment(port=5555)
    
    # Wait for data
    print("Waiting for data from OMNeT++...")
    timeout = 30
    start_time = time.time()
    
    while not env.data_active and time.time() - start_time < timeout:
        time.sleep(1)
        
    if not env.data_active:
        print("❌ No data received within timeout. Check OMNeT++ connection.")
        return
    
    # Create agent
    agent, tf_env, lr_scheduler = create_ppo_agent(env)
    
    # Run diagnostic
    diagnostic = LossDiagnostic(env, agent, tf_env)
    diagnostic.run_full_diagnostic()
    
    # Cleanup
    env.close()

if __name__ == "__main__":
    main()
