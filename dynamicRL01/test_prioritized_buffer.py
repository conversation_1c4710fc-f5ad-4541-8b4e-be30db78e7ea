"""
Test script for TFPrioritizedReplayBuffer.
"""

from tf_prioritized_replay_buffer import TFPrioritizedReplayBuffer
import tensorflow as tf
from tf_agents.specs import tensor_spec

def main():
    # Create buffer
    print("Creating buffer...")
    buffer = TFPrioritizedReplayBuffer(
        data_spec=tensor_spec.TensorSpec([1], tf.float32),
        batch_size=1
    )

    # Add data to buffer
    print("Adding data to buffer...")
    for i in range(10):
        buffer.add_batch(tf.ones([1, 1]) * i)

    # Create dataset
    print("Creating dataset...")
    dataset = buffer.as_dataset(
        sample_batch_size=2,
        num_steps=2
    )

    # Get experience
    print("Getting experience from dataset...")
    iterator = iter(dataset)
    experience, buffer_info = next(iterator)

    print('Successfully retrieved experience from dataset')
    print(f'Experience shape: {tf.nest.map_structure(lambda x: x.shape, experience)}')
    print(f'Buffer info IDs shape: {buffer_info.ids.shape}')
    print(f'Buffer info probabilities shape: {buffer_info.probabilities.shape}')

    # Calculate importance weights
    print("Calculating importance weights...")
    N = tf.cast(buffer.num_frames(), tf.float32)
    probs = buffer_info.probabilities
    beta = buffer.get_beta()
    importance_weights = tf.pow(N * probs, -beta)
    importance_weights = importance_weights / tf.reduce_max(importance_weights)

    print(f'Importance weights shape: {importance_weights.shape}')
    print('Successfully calculated importance weights')

    # Update priorities
    print("Updating priorities...")
    new_priorities = tf.ones_like(buffer_info.ids, dtype=tf.float32)
    buffer.update_priorities(buffer_info.ids, new_priorities)

    print('Successfully updated priorities')
    print("All tests passed!")

if __name__ == "__main__":
    main()
