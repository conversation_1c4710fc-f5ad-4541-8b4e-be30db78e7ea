import optuna
import sys

def check_study(db_path, study_name):
    """Check the status of an Optuna study."""
    try:
        # Connect to the database
        storage_name = f"sqlite:///{db_path}"
        print(f"Connecting to {storage_name} with study name {study_name}")
        
        # Load the study
        study = optuna.load_study(study_name=study_name, storage=storage_name)
        
        # Print study information
        print(f"\nStudy: {study_name}")
        print(f"Number of trials: {len(study.trials)}")
        
        # Count trials by state
        completed = 0
        pruned = 0
        failed = 0
        running = 0
        
        for trial in study.trials:
            if trial.state == optuna.trial.TrialState.COMPLETE:
                completed += 1
            elif trial.state == optuna.trial.TrialState.PRUNED:
                pruned += 1
            elif trial.state == optuna.trial.TrialState.FAIL:
                failed += 1
            elif trial.state == optuna.trial.TrialState.RUNNING:
                running += 1
        
        print(f"Completed trials: {completed}")
        print(f"Pruned trials: {pruned}")
        print(f"Failed trials: {failed}")
        print(f"Running trials: {running}")
        
        # Print best trial if available
        if completed > 0:
            print(f"\nBest trial: #{study.best_trial.number}")
            print(f"  Value: {study.best_trial.value}")
            print(f"  Params: {study.best_trial.params}")
        else:
            print("\nNo completed trials found.")
        
        # Print all trials
        print("\nAll trials:")
        for i, trial in enumerate(study.trials):
            print(f"Trial #{trial.number} - State: {trial.state}")
            if trial.value is not None:
                print(f"  Value: {trial.value}")
            if trial.params:
                print(f"  Params: {trial.params}")
            print()
            
    except Exception as e:
        print(f"Error checking study: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python check_optuna_db.py <db_path> <study_name>")
        sys.exit(1)
    
    db_path = sys.argv[1]
    study_name = sys.argv[2]
    check_study(db_path, study_name)
