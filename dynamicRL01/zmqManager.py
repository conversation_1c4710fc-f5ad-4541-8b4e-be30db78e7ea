import zmq
import time
import json
import numpy as np
from collections import defaultdict, deque
import threading
import queue
import re
from pprint import pprint
from tensorflow import keras
from tensorflow.keras import layers
import logging
import tensorflow as tf


logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# class ZmqManager:
class TSNGCLEnvironment:
    def __init__(self, port):
        # Initialize ZeroMQ context and socket
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.ROUTER)  # Use ROUTER socket for handling multiple clients
        self.socket.bind(f"tcp://*:{port}")  # Bind the socket to the specified port
        self.socket.setsockopt(zmq.SNDHWM, 10000)  # Set high-water mark for send operations
        self.socket.setsockopt(zmq.RCVHWM, 10000)  # Set high-water mark for receive operations
        self._state_size = 6  # State size for the environment
        # State management variables
        self.active_queues = set()  # Set to track active queues (clients)
        self.state_buffer = {}  # Dictionary to store the latest state for each queue
        self.last_action = {}  # Dictionary to store the last action taken for each queue
        self.action_timestamp = {}  # Dictionary to store the timestamp of the last action for each queue

        self.num_flows = 3
        self.num_durations = 4  # 3 flows + 1 closed duration
        self.total_cycle_time = 10.0  # ms (4+2+2+2)
        # Define state space dimensions based on available metrics
        # - Queue utilization for each flow (3)
        # - Current packet count in each queue (3)
        # - Packet arrival rate for each queue (3)
        # - Current GCL config (4 durations)
        # - Time elapsed in current cycle (1)
        self.state_dim = 3 + 3 + 3 + 4 + 1
        
        # Action space: adjustments to the 4 durations
        self.action_dim = 4
        
        # Keep track of episode stats
        self.episode_step = 0
        self.episode_reward = 0.0

        # Response management variables
        self.response_queues = defaultdict(queue.Queue)  # Queues for sending responses to clients
        history_size = 100
        self.state_history = defaultdict(lambda: deque(maxlen=history_size))
        self.state_batch = defaultdict(list)  # Store batch for every 5ms
        self.start_time = defaultdict(lambda: None)  # Track start time for each queue
        self.state_history = defaultdict(lambda: deque(maxlen=history_size))
        self.last_stored_time = defaultdict(lambda: -5)  # Track last stored time for each queue
        self.valid_queue_pattern = re.compile(
            r"S0\.eth\[2\]\.macLayer.queue.queue\[[0-2]\]$|S[1-2]\.eth\[1\]\.macLayer.queue.queue\[[0-2]\]$"
        )
        # Background thread management
        self.running = True  # Flag to control the background thread
        self.state_thread = threading.Thread(target=self._state_monitor)  # Thread for monitoring states
        self.state_thread.daemon = True  # Set thread as daemon so it exits when the main program exits
        self.state_thread.start()
        print("ZMQ Manager initialized")

    def _state_monitor(self):
        """
        Background thread to continuously monitor and update states.
        This thread listens for incoming messages, processes them, and updates the state buffer.
        """
        poller = zmq.Poller()  # Create a poller object
        poller.register(self.socket, zmq.POLLIN)  # Register the socket to listen for incoming messages

        while self.running:
            try:
                # Poll the socket with a timeout (e.g., 1000 ms)
                events = dict(poller.poll(timeout=1000))

                if self.socket in events and events[self.socket] == zmq.POLLIN:
                    # Receive a multipart message from the socket
                    identity, _, message = self.socket.recv_multipart()
                    data = json.loads(message.decode())  # Decode and parse the JSON message
                    queue_id_raw = identity.decode()  # Use the identity as the queue ID
                    queue_id = queue_id_raw.split("testCaseFour.")[-1]  # Extract after prefix
                    # ✅ Check if queue_id matches desired pattern
                    if not self.valid_queue_pattern.match(queue_id):
                        print(f"Ignoring queue: {queue_id}")
                        continue

                    # ✅ Valid queue, process further
                    current_time_ms = round(float(data['state'][5]) * 1000, 2)

                    current_state = {
                        "num_packets": float(data['state'][0]),
                        "queue_occupancy": float(data['state'][1]),
                        "packetArrivalRate": float(data['state'][2]),
                        "deadline": float(data['state'][3]),
                        "existing_gcl": np.array(data['state'][4]),
                        "current_sim_time": current_time_ms
                    }

                    # ✅ If batch time exceeds 5ms, send to agent and reset
                    if self.start_time.get(queue_id) is None or (current_time_ms - self.start_time[queue_id]) >= 5:
                        if self.state_batch.get(queue_id):
                            self._handle_agent_action(queue_id, self.state_batch[queue_id])
                        self.state_batch[queue_id] = []
                        self.start_time[queue_id] = current_time_ms

                    self.state_batch[queue_id].append(current_state)
                    self.state_history.setdefault(queue_id, []).append(current_state)

                    # print(f"State stored for queue {queue_id} at {current_time_ms} ms")

            except zmq.Again:
                print("No message received")
            except Exception as e:
                print(f"Error in state monitor: {e}")

    def _get_latest_states(self):
        """Get the most recent states from all active queues"""
        states = []
        for queue_id in self.active_queues:
            states.append(self.state_buffer[queue_id])
        return np.mean(states, axis=0) if states else np.zeros(self._state_size)

    def _handle_agent_action(self, queue_id, state_batch):
        """Pass collected 5ms batch to PPO agent."""
        # Pass the batch of states collected over 5ms to the PPO agent
        if state_batch:
            # print(f"Passing batch of {len(state_batch)} states to PPO agent for queue {queue_id} for time : {state_batch[0]['current_sim_time']} ms")
            # Extract values for mean calculation
            num_packets = [d["num_packets"] for d in state_batch]
            queue_occupancy = [d["queue_occupancy"] for d in state_batch]
            packet_arrival_rate = [d["packetArrivalRate"] for d in state_batch]
            deadlines = [d["deadline"] for d in state_batch]
            # print("deadline", deadlines)

            # Calculate means
            state = {
                "queue_id": queue_id,
                "mean_num_packets": np.mean(num_packets),
                "mean_queue_occupancy": np.mean(queue_occupancy),
                "mean_packet_arrival_rate": np.mean(packet_arrival_rate),
                "mean_deadline": np.mean(deadlines),
            }
            # Check GCL array changes
            gcl_list = [d["existing_gcl"].tolist() for d in state_batch]
            test = []
            for gcl in gcl_list:
                gcl = list(gcl)
                gcl = "".join(gcl)
                gcl_numbers = [int(num) for num in re.findall(r'\d+', gcl)]
                test.append(tuple(gcl_numbers))
            gcl_list = test
            unique_gcls = set(gcl_list)

            if len(unique_gcls) == 1:
                 state["gcl"] = list(unique_gcls.pop())
            else:
                state["gcl"] = [list(gcl) for gcl in unique_gcls]

            # Print results
            print("State: ", state)
            # print("State sent to agent:", state)
            # self.dataToAgent(state)
            return state

        else:
            print(f"No states collected for queue {queue_id} in this 5ms window")
    
    # def dataToAgent(self, state):
    #     """Get the most recent states from all active queues"""
    #     print("Data sent to agent:", state)
    #     return
    def reset(self):
        """Reset the environment and get initial state."""
        # Reset episode tracking
        self.episode_step = 0
        self.episode_reward = 0.0
        return np.zeros(self.state_dim)
    
    def apply_action(self, gcl_durations):
        """Apply the new GCL durations to the simulation."""
        # Ensure durations are valid (sum to total cycle time)
        durations = np.array(gcl_durations)
        if not np.isclose(np.sum(durations), self.total_cycle_time):
            # Normalize to maintain total cycle time
            durations = durations * (self.total_cycle_time / np.sum(durations))
        
        # Round to nearest 0.1ms for practical implementation
        durations = np.round(durations, 1)
        
        # Send new GCL configuration
        command = {
            "command": "set_gcl",
            "durations": durations.tolist()
        }
        self.socket.send_json(command)

        return {"reward": 0.0, "done": False}        
        # Receive response with reward and new state info
        # try:
        #     response = self.socket.recv_json()
        #     if response["status"] != "ok":
        #         logger.error(f"Error applying action: {response.get('error', 'Unknown error')}")
        #         return {"reward": 0.0, "done": True}
            
        #     return {
        #         "reward": response["reward"],
        #         "done": response.get("done", False),
        #         "info": response.get("info", {})
        #     }
        # except Exception as e:
        #     logger.error(f"Exception while applying action: {str(e)}")
        #     return {"reward": 0.0, "done": True}

    def step(self, action):
        """Take a step in the environment with the given action."""
        # Convert normalized action to actual GCL durations
        # gcl_durations = self._action_to_gcl_durations(action)
        
        # Apply action and get results
        result = self.apply_action(action)
        reward = result["reward"]
        done = result["done"]
        info = result.get("info", {})
        
        # Get new state
        next_state = self._handle_agent_action()
        
        # Update episode tracking
        self.episode_step += 1
        self.episode_reward += reward
        
        # Log progress periodically
        if self.episode_step % 10 == 0:
            print(f"Step {self.episode_step}, Episode Reward: {self.episode_reward}")
            print(f"Applied GCL durations: {action}")
        
        return next_state, reward, done, info
    
    def listen_for_data(self):
        try:
            # Keep the main thread running to allow the background thread to process messages
            print("Press Ctrl+C to exit...")
            while True:
                time.sleep(1)  # Sleep to prevent busy waiting
        except KeyboardInterrupt:
            print("Shutting down ZMQ Manager...")
            self.close()

    def close(self):
        """Cleanup resources"""
        self.running = False
        self.state_thread.join()
        self.socket.close()
        self.context.term()

class Buffer:
    """Experience replay buffer for PPO."""
    
    def __init__(self, state_dim, action_dim, buffer_capacity=4000, batch_size=128):
        self.buffer_capacity = buffer_capacity
        self.batch_size = batch_size
        self.buffer_counter = 0
        self.filled = False
        
        # Storing experiences
        self.states = np.zeros((buffer_capacity, state_dim), dtype=np.float32)
        self.actions = np.zeros((buffer_capacity, action_dim), dtype=np.float32)
        self.logprobs = np.zeros((buffer_capacity,), dtype=np.float32)
        self.rewards = np.zeros((buffer_capacity,), dtype=np.float32)
        self.next_states = np.zeros((buffer_capacity, state_dim), dtype=np.float32)
        self.dones = np.zeros((buffer_capacity,), dtype=np.float32)
        self.values = np.zeros((buffer_capacity,), dtype=np.float32)
    
    def record(self, state, action, logprob, reward, next_state, done, value):
        """Store experience in buffer."""
        index = self.buffer_counter % self.buffer_capacity
        
        self.states[index] = state
        self.actions[index] = action
        self.logprobs[index] = logprob
        self.rewards[index] = reward
        self.next_states[index] = next_state
        self.dones[index] = done
        self.values[index] = value
        
        self.buffer_counter += 1
        if self.buffer_counter >= self.buffer_capacity:
            self.filled = True
    
    def get_minibatch(self):
        """Sample a minibatch from the buffer."""
        max_length = min(self.buffer_counter, self.buffer_capacity)
        batch_indices = np.random.choice(max_length, min(self.batch_size, max_length), replace=False)
        
        return (
            self.states[batch_indices],
            self.actions[batch_indices],
            self.logprobs[batch_indices],
            self.rewards[batch_indices],
            self.next_states[batch_indices],
            self.dones[batch_indices],
            self.values[batch_indices]
        )
    
    def is_ready(self):
        """Check if buffer has enough data for training."""
        return self.buffer_counter >= self.batch_size
    
    def clear(self):
        """Clear the buffer."""
        self.buffer_counter = 0
        self.filled = False


class Actor(keras.Model):
    """Actor network for PPO."""
    
    def __init__(self, state_dim, action_dim, hidden_units= [256, 128], name="actor"):
        super(Actor, self).__init__(name=name)
        
        self.hidden_layers = []
        for units in hidden_units:
            self.hidden_layers.append(layers.Dense(units, activation='relu'))
        
        self.mu = layers.Dense(action_dim, activation='tanh')  # Output in [-1, 1]
        self.log_sigma = layers.Dense(action_dim, activation='linear')
        
        # Build model
        dummy_state = tf.convert_to_tensor(np.zeros((1, state_dim)), dtype=tf.float32)
        self(dummy_state)
    
    def call(self, state):
        x = state
        for layer in self.hidden_layers:
            x = layer(x)
        
        mu = self.mu(x)
        log_sigma = self.log_sigma(x)
        
        # Clip log_sigma for stability
        log_sigma = tf.clip_by_value(log_sigma, -20, 2)
        sigma = tf.exp(log_sigma)
        
        return mu, sigma
    
    def get_action(self, state):
        """Sample action from policy distribution."""
        state = tf.convert_to_tensor([state], dtype=tf.float32)
        mu, sigma = self(state)
        
        # Create normal distribution
        dist = tf.compat.v1.distributions.Normal(loc=mu, scale=sigma)
        
        # Sample action
        action = dist.sample()
        action = tf.clip_by_value(action, -1.0, 1.0)
        
        # Compute log probability
        logprob = dist.log_prob(action)
        logprob = tf.reduce_sum(logprob, axis=-1)
        
        return action[0].numpy(), logprob[0].numpy()
    
    def log_prob(self, state, action):
        """Compute log probability of action under policy."""
        state = tf.convert_to_tensor(state, dtype=tf.float32)
        action = tf.convert_to_tensor(action, dtype=tf.float32)
        
        mu, sigma = self(state)
        dist = tf.compat.v1.distributions.Normal(loc=mu, scale=sigma)
        
        logprob = dist.log_prob(action)
        return tf.reduce_sum(logprob, axis=-1)


class Critic(keras.Model):
    """Critic network for PPO."""
    
    def __init__(self, state_dim: int, hidden_units= [256, 128], name="critic"):
        super(Critic, self).__init__(name=name)
        
        self.hidden_layers = []
        for units in hidden_units:
            self.hidden_layers.append(layers.Dense(units, activation='relu'))
        
        self.value = layers.Dense(1)
        
        # Build model
        dummy_state = tf.convert_to_tensor(np.zeros((1, state_dim)), dtype=tf.float32)
        self(dummy_state)
    
    def call(self, state):
        x = state
        for layer in self.hidden_layers:
            x = layer(x)
        
        return self.value(x)
    
class PPOAgent:
    """PPO agent with continuous action space for TSN GCL optimization."""
    
    def __init__(
        self,
        state_dim: int,
        action_dim: int,
        env: TSNGCLEnvironment,
        actor_lr: float = 3e-4,
        critic_lr: float = 1e-3,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        clip_ratio: float = 0.2,
        target_kl: float = 0.01,
        update_epochs: int = 10,
        buffer_capacity: int = 4000,
        batch_size: int = 128
    ):
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.env = env
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.target_kl = target_kl
        self.update_epochs = update_epochs
        
        # Initialize actor-critic models
        self.actor = Actor(state_dim, action_dim)
        self.critic = Critic(state_dim)
        
        # Initialize optimizers
        self.actor_optimizer = tf.keras.optimizers.Adam(learning_rate=actor_lr)
        self.critic_optimizer = tf.keras.optimizers.Adam(learning_rate=critic_lr)
        
        # Initialize experience buffer
        self.buffer = Buffer(state_dim, action_dim, buffer_capacity, batch_size)
        
        # Training stats
        self.policy_loss_history = []
        self.value_loss_history = []
        self.reward_history = []
    
    def get_action_and_value(self, state):
        """Get action and value from current policy."""
        # Get raw action and log probability from actor
        raw_action, logprob = self.actor.get_action(state)
        
        # Get value estimate from critic
        value = self.critic(tf.convert_to_tensor([state], dtype=tf.float32))
        value = value.numpy()[0, 0]
        
        return raw_action, value, logprob, value
    
    def train(self, timesteps_per_update, max_timesteps):
        """Train the agent."""
        start_time = time.time()
        state = self.env.reset()
        episode_reward = 0
        episode_length = 0
        total_timesteps = 0
        episodes = 0
        
        logger.info("Starting training...")
        
        while total_timesteps < max_timesteps:
            # Collect experience for one update
            for _ in range(timesteps_per_update):
                # Get action and value
                action, value, logprob, _ = self.get_action_and_value(state)
                
                # Take step in environment
                next_state, reward, done, _ = self.env.step(action)
                
                # Store transition
                self.buffer.record(state, action, logprob, reward, next_state, float(done), value)
                
                state = next_state
                episode_reward += reward
                episode_length += 1
                total_timesteps += 1
                
                # Check if episode is done
                if done:
                    self.reward_history.append(episode_reward)
                    
                    # Log episode stats
                    logger.info(f"Episode {episodes} | Reward: {episode_reward:.2f} | Length: {episode_length}")
                    
                    # Reset for next episode
                    state = self.env.reset()
                    episode_reward = 0
                    episode_length = 0
                    episodes += 1
            
            # Update policy and value networks
            if self.buffer.is_ready():
                policy_loss, value_loss, kl = self.update()
                self.policy_loss_history.append(policy_loss)
                self.value_loss_history.append(value_loss)
                
                # Log update stats
                elapsed_time = time.time() - start_time
                logger.info(f"Timestep: {total_timesteps} | Policy Loss: {policy_loss:.4f} | Value Loss: {value_loss:.4f} | KL: {kl:.4f} | Time: {elapsed_time:.2f}s")
                
                # Early stopping based on KL divergence
                if kl > 1.5 * self.target_kl:
                    logger.info(f"Early stopping at step {total_timesteps} due to reaching max KL divergence")
                    break
            
            # Save model periodically
            if total_timesteps % 10000 == 0:
                self.save_models(f"actor_model_{total_timesteps}.h5", f"critic_model_{total_timesteps}.h5")
        
        # Save final model
        self.save_models("actor_model_final.h5", "critic_model_final.h5")
        logger.info("Training completed")
    
    def update(self):
        """Update policy and value networks using PPO."""
        # Get data from buffer
        states, actions, old_logprobs, rewards, next_states, dones, old_values = self.buffer.get_minibatch()
        
        # Convert to tensors
        states = tf.convert_to_tensor(states, dtype=tf.float32)
        actions = tf.convert_to_tensor(actions, dtype=tf.float32)
        old_logprobs = tf.convert_to_tensor(old_logprobs, dtype=tf.float32)
        rewards = tf.convert_to_tensor(rewards, dtype=tf.float32)
        next_states = tf.convert_to_tensor(next_states, dtype=tf.float32)
        dones = tf.convert_to_tensor(dones, dtype=tf.float32)
        old_values = tf.convert_to_tensor(old_values, dtype=tf.float32)
        
        # Compute advantages and returns
        next_values = self.critic(next_states)
        next_values = tf.squeeze(next_values)
        
        # Calculate returns and advantages
        returns = self._compute_returns(rewards, old_values, next_values, dones)
        advantages = returns - old_values
        
        # Normalize advantages
        advantages = (advantages - tf.reduce_mean(advantages)) / (tf.math.reduce_std(advantages) + 1e-8)
        
        # Multiple epoch updates
        policy_loss = 0
        value_loss = 0
        kl_divergence = 0
        
        for _ in range(self.update_epochs):
            # Update policy
            with tf.GradientTape() as tape:
                # Get current log probabilities
                current_logprobs = self.actor.log_prob(states, actions)
                
                # Compute probability ratio
                ratio = tf.exp(current_logprobs - old_logprobs)
                
                # Compute surrogate objectives
                surrogate1 = ratio * advantages
                surrogate2 = tf.clip_by_value(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * advantages
                
                # Compute policy loss (negative because we're maximizing)
                policy_loss = -tf.reduce_mean(tf.minimum(surrogate1, surrogate2))
            
            # Get gradients and update actor
            actor_gradients = tape.gradient(policy_loss, self.actor.trainable_variables)
            self.actor_optimizer.apply_gradients(zip(actor_gradients, self.actor.trainable_variables))
            
            # Update value function
            with tf.GradientTape() as tape:
                values = self.critic(states)
                values = tf.squeeze(values)
                value_loss = tf.reduce_mean(tf.square(returns - values))
            
            # Get gradients and update critic
            critic_gradients = tape.gradient(value_loss, self.critic.trainable_variables)
            self.critic_optimizer.apply_gradients(zip(critic_gradients, self.critic.trainable_variables))
            
            # Compute KL divergence for early stopping
            kl_divergence = tf.reduce_mean(old_logprobs - self.actor.log_prob(states, actions))
            
            # Early stopping based on KL divergence
            if kl_divergence > 1.5 * self.target_kl:
                break
        
        return policy_loss.numpy(), value_loss.numpy(), kl_divergence.numpy()
    
    def _compute_returns(self, rewards, values, next_values, dones):
        """Compute returns using GAE."""
        returns = tf.TensorArray(dtype=tf.float32, size=len(rewards))
        advantages = tf.zeros_like(rewards)
        last_gae_lambda = 0
        
        for t in reversed(range(len(rewards))):
            # Calculate TD target
            next_val = next_values[t]
            if dones[t]:
                next_val = 0
            
            # Calculate delta
            delta = rewards[t] + self.gamma * next_val - values[t]
            
            # Calculate GAE
            last_gae_lambda = delta + self.gamma * self.gae_lambda * (1 - dones[t]) * last_gae_lambda
            
            # Store return
            returns = returns.write(t, last_gae_lambda + values[t])
        
        returns = returns.stack()
        return returns
    
    def save_models(self, actor_path, critic_path):
        """Save actor and critic models."""
        self.actor.save_weights(actor_path)
        self.critic.save_weights(critic_path)
        logger.info(f"Models saved to {actor_path} and {critic_path}")
    
    def load_models(self, actor_path, critic_path):
        """Load actor and critic models."""
        self.actor.load_weights(actor_path)
        self.critic.load_weights(critic_path)
        logger.info(f"Models loaded from {actor_path} and {critic_path}")


class RewardCalculator:
    """Calculates rewards based on available queue metrics."""
    
    def __init__(self, priority_weights):
        # Weights for different priority queues
        self.priority_weights = priority_weights if priority_weights else [3.0, 2.0, 1.0]
        
        # Reward component weights
        self.queue_balance_weight = 0.4
        self.priority_service_weight = 0.6
    
    def calculate_reward(self, queue_metrics):
        """Calculate reward based on queue metrics."""
        # Extract metrics
        queue_utilization = queue_metrics["queue_utilization"]
        packet_counts = queue_metrics["packet_counts"]
        service_rates = queue_metrics["service_rates"]  # Packets processed per cycle
        
        # Calculate queue balancing score
        queue_balance_score = self._calculate_queue_balance(queue_utilization, packet_counts)
        
        # Calculate priority-weighted service rate
        priority_service_score = self._calculate_priority_service(service_rates)
        
        # Combine reward components
        reward = (
            self.queue_balance_weight * queue_balance_score +
            self.priority_service_weight * priority_service_score
        )
        
        return reward
    
    def _calculate_queue_balance(self, queue_utilization):
        """Calculate score based on how balanced the queues are."""
        # Penalize high standard deviation in queue utilization (normalized)
        if len(queue_utilization) <= 1:
            return 0.0
        
        # Calculate standard deviation of utilization
        util_std = np.std(queue_utilization)
        
        # Penalize high standard deviation (imbalance)
        balance_score = 1.0 - min(util_std, 1.0)
        
        return balance_score
    
    def _calculate_priority_service(self, service_rates):
        """Calculate score based on how well high-priority queues are serviced."""
        if not service_rates or len(service_rates) != len(self.priority_weights):
            return 0.0
        
        # Calculate priority-weighted service rate
        weighted_service = 0.0
        total_weight = sum(self.priority_weights)
        
        for i, rate in enumerate(service_rates):
            weighted_service += rate * self.priority_weights[i]
        
        # Normalize
        weighted_service /= total_weight
        
        return weighted_service


def main():
    """Main function to set up and run the TSN GCL optimization."""
    # Configuration
    zmq_address = "tcp://127.0.0.1:5555"
    priority_weights = [3.0, 2.0, 1.0]  # Higher weight for higher priority
    
    # Initialize environment
    # env = TSNGCLEnvironment(zmq_address, priority_weights)
    env = TSNGCLEnvironment(5555)
    
    # Initialize PPO agent
    agent = PPOAgent(
        state_dim=env.state_dim,
        action_dim=env.action_dim,
        env=env,
        actor_lr=3e-4,
        critic_lr=1e-3,
        gamma=0.99,
        gae_lambda=0.95,
        clip_ratio=0.2,
        target_kl=0.01,
        update_epochs=10,
        buffer_capacity=4000,
        batch_size=128
    )
    
    # Train agent
    try:
        agent.train(timesteps_per_update=1000, max_timesteps=1000000)
    except KeyboardInterrupt:
        print("Training interrupted by user")
    finally:
        # Save final model
        agent.save_models("actor_model_final.h5", "critic_model_final.h5")
    
    print("Training completed")


if __name__ == "__main__":
    main()

# Create an instance of ZmqManager
# zmq_manager = ZmqManager(5555)
# zmq_manager.listen_for_data()
