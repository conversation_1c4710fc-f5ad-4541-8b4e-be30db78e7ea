"""
Test script for the TFPrioritizedReplayBuffer implementation.

This script creates a simple environment and agent to test the
TFPrioritizedReplayBuffer implementation.
"""

import tensorflow as tf
import numpy as np
from tf_agents.environments import suite_gym
from tf_agents.environments import tf_py_environment
from tf_agents.networks import q_network
from tf_agents.agents.dqn import dqn_agent
from tf_agents.utils import common
from tf_agents.trajectories import trajectory
from tf_prioritized_replay_buffer import TFPrioritizedReplayBuffer

def test_prioritized_replay():
    """Test the TFPrioritizedReplayBuffer implementation."""
    print("Testing TFPrioritizedReplayBuffer...")
    
    # Create a simple environment
    env_name = "CartPole-v1"
    py_env = suite_gym.load(env_name)
    tf_env = tf_py_environment.TFPyEnvironment(py_env)
    
    # Create a simple Q-network
    q_net = q_network.QNetwork(
        tf_env.observation_spec(),
        tf_env.action_spec(),
        fc_layer_params=(100,)
    )
    
    # Create a DQN agent
    optimizer = tf.keras.optimizers.Adam(learning_rate=1e-3)
    train_step_counter = tf.Variable(0)
    
    agent = dqn_agent.DqnAgent(
        tf_env.time_step_spec(),
        tf_env.action_spec(),
        q_network=q_net,
        optimizer=optimizer,
        td_errors_loss_fn=common.element_wise_squared_loss,
        train_step_counter=train_step_counter
    )
    agent.initialize()
    
    # Create a prioritized replay buffer
    replay_buffer = TFPrioritizedReplayBuffer(
        data_spec=agent.collect_data_spec,
        batch_size=tf_env.batch_size,
        max_length=1000,
        alpha=0.6,
        beta=0.4,
        epsilon=1e-6
    )
    
    # Collect some data
    time_step = tf_env.reset()
    for _ in range(100):
        action_step = agent.collect_policy.action(time_step)
        next_time_step = tf_env.step(action_step.action)
        traj = trajectory.from_transition(time_step, action_step, next_time_step)
        replay_buffer.add_batch(traj)
        time_step = next_time_step
        
        if time_step.is_last():
            time_step = tf_env.reset()
    
    # Check that the buffer has data
    print(f"Replay buffer has {replay_buffer.num_frames()} frames")
    
    # Sample from the buffer
    dataset = replay_buffer.as_dataset(
        num_parallel_calls=3,
        sample_batch_size=64,
        num_steps=2
    ).prefetch(3)
    
    # Get experience and buffer info
    iterator = iter(dataset)
    experience, buffer_info = next(iterator)
    
    # Check that we got the expected data
    print(f"Sampled experience shape: {tf.nest.map_structure(lambda x: x.shape, experience)}")
    print(f"Buffer info IDs shape: {buffer_info.ids.shape}")
    print(f"Buffer info probabilities shape: {buffer_info.probabilities.shape}")
    
    # Calculate importance sampling weights
    N = replay_buffer.num_frames()
    probs = buffer_info.probabilities
    beta = replay_buffer.get_beta()
    importance_weights = tf.pow(N * probs, -beta)
    importance_weights = importance_weights / tf.reduce_max(importance_weights)
    
    print(f"Importance weights shape: {importance_weights.shape}")
    print(f"Importance weights min: {tf.reduce_min(importance_weights)}")
    print(f"Importance weights max: {tf.reduce_max(importance_weights)}")
    
    # Train with importance sampling weights
    train_loss = agent.train(experience, weights=importance_weights)
    
    # Update priorities based on TD error
    td_errors = tf.abs(train_loss.extra.td_loss)
    new_priorities = td_errors + 1e-6
    replay_buffer.update_priorities(buffer_info.ids, new_priorities)
    
    print(f"TD errors shape: {td_errors.shape}")
    print(f"New priorities shape: {new_priorities.shape}")
    
    # Test annealing beta
    replay_buffer.set_beta(0.5)
    print(f"Updated beta: {replay_buffer.get_beta()}")
    
    print("Test completed successfully!")
    return True

if __name__ == "__main__":
    test_prioritized_replay()
