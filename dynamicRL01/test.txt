b'testCaseFour.S0.eth[2].macLayer.queue.queue[1]',
b'testCaseFour.S1.eth[1].macLayer.queue.queue[2]', 
b'testCaseFour.S0.eth[1].macLayer.queue.queue[0]', 
b'testCaseFour.S0.eth[2].macLayer.queue.queue[0]', 
b'testCaseFour.S0.eth[0].macLayer.queue.queue[2]', 
b'testCaseFour.S0.eth[0].macLayer.queue.queue[0]', 
b'testCaseFour.S1.eth[1].macLayer.queue.queue[1]', 
b'testCaseFour.S0.eth[0].macLayer.queue.queue[1]', 
b'testCaseFour.S0.eth[1].macLayer.queue.queue[2]', 
b'testCaseFour.S2.eth[1].macLayer.queue.queue[2]', 
b'testCaseFour.S2.eth[1].macLayer.queue.queue[1]', 
b'testCaseFour.S0.eth[2].macLayer.queue.queue[2]', 
b'testCaseFour.S0.eth[1].macLayer.queue.queue[1]'