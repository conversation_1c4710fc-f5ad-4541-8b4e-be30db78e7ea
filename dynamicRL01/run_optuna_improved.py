import optuna
import logging
import os
import sys
import time
import json
from revamped_new import optuna_objective

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    filename='optuna_improved.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

def main():
    """Run Optuna hyperparameter optimization for TSNGCLEnvironment with learning-focused evaluation."""
    print("Starting learning-focused Optuna hyperparameter optimization for TSNGCLEnvironment")
    print("This optimization prioritizes learning capability and stability over raw performance scores")
    logging.info("Starting learning-focused Optuna hyperparameter optimization for TSNGCLEnvironment")
    
    # Create a new study with a different name to avoid conflicts
    study_name = "tsngcl_optimization_improved"
    storage_name = "sqlite:///optuna_improved.db"
    
    # Add pruning to stop unpromising trials early
    pruner = optuna.pruners.MedianPruner(
        n_startup_trials=10,  # Number of trials to run before pruning starts
        n_warmup_steps=20,   # Number of steps to run before pruning starts
        interval_steps=1     # Interval between pruning checks
    )
    
    try:
        # Create a new study (load_if_exists=True to continue if it exists)
        study = optuna.create_study(
            study_name=study_name,
            storage=storage_name,
            load_if_exists=True,  # Continue if it exists
            direction="maximize",  # We want to maximize the learning quality score
            pruner=pruner  # Use the pruner to stop unpromising trials early
        )
        
        print(f"Study '{study_name}' created or loaded successfully")
        logging.info(f"Study '{study_name}' created or loaded successfully")
        
        # Print study statistics if it already exists
        if len(study.trials) > 0:
            print(f"Study already has {len(study.trials)} trials")
            logging.info(f"Study already has {len(study.trials)} trials")
            
            # Print best trial so far
            print(f"Best trial so far: #{study.best_trial.number}")
            print(f"  Value: {study.best_trial.value}")
            print(f"  Params: {study.best_trial.params}")
            logging.info(f"Best trial so far: #{study.best_trial.number}")
            logging.info(f"  Value: {study.best_trial.value}")
            logging.info(f"  Params: {study.best_trial.params}")
        
        # Set the number of trials to run
        n_trials = int(input("Enter the number of trials to run (default: 20): ") or "20")
        
        # Run the optimization
        print(f"Starting optimization with {n_trials} trials")
        logging.info(f"Starting optimization with {n_trials} trials")
        
        # Add a callback to save intermediate results
        def save_intermediate_results(study, trial):
            """Save intermediate results after each trial."""
            if trial.state == optuna.trial.TrialState.COMPLETE:
                print(f"Trial #{trial.number} completed with value: {trial.value}")
                print(f"  Params: {trial.params}")

                # Print detailed metrics if available
                if hasattr(trial, 'user_attrs') and trial.user_attrs:
                    print(f"  Detailed metrics:")
                    for key, value in trial.user_attrs.items():
                        if isinstance(value, float):
                            print(f"    {key}: {value:.4f}")
                        else:
                            print(f"    {key}: {value}")

                # Save detailed study information to JSON for further analysis
                study_info = {
                    "best_trial": {
                        "number": study.best_trial.number,
                        "value": study.best_trial.value,
                        "params": study.best_trial.params,
                        "user_attrs": getattr(study.best_trial, 'user_attrs', {})
                    },
                    "trials": []
                }

                # Add information about all completed trials
                for t in study.trials:
                    if t.state == optuna.trial.TrialState.COMPLETE:
                        trial_info = {
                            "number": t.number,
                            "value": t.value,
                            "params": t.params
                        }
                        # Include user attributes if available
                        if hasattr(t, 'user_attrs') and t.user_attrs:
                            trial_info["user_attrs"] = t.user_attrs
                        study_info["trials"].append(trial_info)

                # Save to JSON file
                with open("optuna_intermediate_results.json", "w") as f:
                    json.dump(study_info, f, indent=2)

                print(f"Intermediate results saved to optuna_intermediate_results.json")
        
        # Run the optimization with the callback
        study.optimize(
            optuna_objective, 
            n_trials=n_trials, 
            timeout=None,
            callbacks=[save_intermediate_results]
        )
        
        # Print optimization results
        print("Learning-focused optimization completed successfully")
        print(f"Best learning trial: #{study.best_trial.number}")
        print(f"  Learning Quality Score: {study.best_trial.value:.4f}")
        print(f"  Hyperparameters: {study.best_trial.params}")

        # Print detailed learning metrics for best trial
        if hasattr(study.best_trial, 'user_attrs') and study.best_trial.user_attrs:
            print(f"  Learning Analysis for Best Trial:")
            attrs = study.best_trial.user_attrs

            # Prioritize key learning metrics in display
            key_metrics = ['loss_improvement', 'return_improvement', 'loss_stability', 'avg_loss', 'avg_return']
            for metric in key_metrics:
                if metric in attrs:
                    value = attrs[metric]
                    if isinstance(value, float):
                        print(f"    {metric}: {value:.4f}")
                    else:
                        print(f"    {metric}: {value}")

            # Show other metrics
            print(f"    Other metrics:")
            for key, value in attrs.items():
                if key not in key_metrics:
                    if isinstance(value, float):
                        print(f"      {key}: {value:.4f}")
                    else:
                        print(f"      {key}: {value}")

        logging.info("Learning-focused optimization completed successfully")
        logging.info(f"Best learning trial: #{study.best_trial.number}")
        logging.info(f"  Learning Quality Score: {study.best_trial.value:.4f}")
        logging.info(f"  Hyperparameters: {study.best_trial.params}")

        # Log detailed learning metrics for best trial
        if hasattr(study.best_trial, 'user_attrs') and study.best_trial.user_attrs:
            logging.info(f"  Learning Analysis for Best Trial:")
            for key, value in study.best_trial.user_attrs.items():
                logging.info(f"    {key}: {value}")
        
        # Save the best parameters to a file for easy reference
        with open("best_params_improved.txt", "w") as f:
            f.write(f"Best Learning Trial: #{study.best_trial.number}\n")
            f.write(f"Learning Quality Score: {study.best_trial.value:.4f}\n")
            f.write("Hyperparameters:\n")
            for param_name, param_value in study.best_trial.params.items():
                f.write(f"  {param_name}: {param_value}\n")

            # Add learning analysis if available
            if hasattr(study.best_trial, 'user_attrs') and study.best_trial.user_attrs:
                f.write("\nLearning Analysis:\n")
                attrs = study.best_trial.user_attrs

                # Write key learning metrics first
                key_metrics = ['loss_improvement', 'return_improvement', 'loss_stability', 'avg_loss', 'avg_return']
                for metric in key_metrics:
                    if metric in attrs:
                        value = attrs[metric]
                        if isinstance(value, float):
                            f.write(f"  {metric}: {value:.4f}\n")
                        else:
                            f.write(f"  {metric}: {value}\n")

                f.write("\nDetailed Metrics:\n")
                for key, value in attrs.items():
                    if key not in key_metrics:
                        if isinstance(value, float):
                            f.write(f"  {key}: {value:.4f}\n")
                        else:
                            f.write(f"  {key}: {value}\n")
        
        # Save detailed study information to JSON for further analysis
        study_info = {
            "best_trial": {
                "number": study.best_trial.number,
                "value": study.best_trial.value,
                "params": study.best_trial.params,
                "user_attrs": getattr(study.best_trial, 'user_attrs', {})
            },
            "trials": []
        }

        # Add information about all trials
        for trial in study.trials:
            if trial.state == optuna.trial.TrialState.COMPLETE:
                trial_info = {
                    "number": trial.number,
                    "value": trial.value,
                    "params": trial.params
                }
                # Include user attributes if available
                if hasattr(trial, 'user_attrs') and trial.user_attrs:
                    trial_info["user_attrs"] = trial.user_attrs
                study_info["trials"].append(trial_info)
        
        # Save to JSON file
        with open("optuna_results_improved.json", "w") as f:
            json.dump(study_info, f, indent=2)
        
        print("Best learning parameters saved to best_params_improved.txt")
        print("Detailed study results saved to optuna_results_improved.json")
        logging.info("Best learning parameters saved to best_params_improved.txt")
        logging.info("Detailed study results saved to optuna_results_improved.json")

        # Analyze learning patterns across trials
        print("\n" + "="*80)
        print("LEARNING ANALYSIS SUMMARY")
        print("="*80)

        completed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE]
        if len(completed_trials) >= 3:
            # Find trials with best learning characteristics
            learning_trials = []
            for trial in completed_trials:
                if hasattr(trial, 'user_attrs') and 'loss_improvement' in trial.user_attrs:
                    learning_trials.append({
                        'number': trial.number,
                        'score': trial.value,
                        'loss_improvement': trial.user_attrs.get('loss_improvement', 0),
                        'return_improvement': trial.user_attrs.get('return_improvement', 0),
                        'loss_stability': trial.user_attrs.get('loss_stability', 0),
                        'params': trial.params
                    })

            if learning_trials:
                # Sort by different criteria
                best_loss_learning = sorted(learning_trials, key=lambda x: x['loss_improvement'], reverse=True)[:3]
                best_return_learning = sorted(learning_trials, key=lambda x: x['return_improvement'], reverse=True)[:3]
                most_stable = sorted(learning_trials, key=lambda x: x['loss_stability'], reverse=True)[:3]

                print(f"Top 3 trials by Loss Improvement:")
                for i, trial in enumerate(best_loss_learning, 1):
                    print(f"  {i}. Trial #{trial['number']}: Loss↓ {trial['loss_improvement']:.4f}, Score: {trial['score']:.4f}")

                print(f"\nTop 3 trials by Return Improvement:")
                for i, trial in enumerate(best_return_learning, 1):
                    print(f"  {i}. Trial #{trial['number']}: Return↑ {trial['return_improvement']:.4f}, Score: {trial['score']:.4f}")

                print(f"\nTop 3 trials by Learning Stability:")
                for i, trial in enumerate(most_stable, 1):
                    print(f"  {i}. Trial #{trial['number']}: Stability {trial['loss_stability']:.4f}, Score: {trial['score']:.4f}")

                print(f"\nRecommendation: Trial #{study.best_trial.number} was selected as it provides the best")
                print(f"overall learning capability, balancing loss reduction, performance improvement, and stability.")

        print("="*80)
        
        # Visualize the optimization results
        try:
            import matplotlib.pyplot as plt
            
            # Create plots directory if it doesn't exist
            os.makedirs("plots", exist_ok=True)
            
            # Plot optimization history
            plt.figure(figsize=(10, 6))
            optuna.visualization.matplotlib.plot_optimization_history(study)
            plt.tight_layout()
            plt.savefig("plots/optimization_history.png")
            
            # Plot parameter importances
            plt.figure(figsize=(10, 6))
            optuna.visualization.matplotlib.plot_param_importances(study)
            plt.tight_layout()
            plt.savefig("plots/param_importances.png")
            
            # Plot parallel coordinate
            plt.figure(figsize=(12, 8))
            optuna.visualization.matplotlib.plot_parallel_coordinate(study)
            plt.tight_layout()
            plt.savefig("plots/parallel_coordinate.png")
            
            print("Visualization plots saved to plots/ directory")
            logging.info("Visualization plots saved to plots/ directory")
        except Exception as e:
            print(f"Error creating visualization plots: {e}")
            logging.error(f"Error creating visualization plots: {e}")
        
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user.")
        logging.info("Optimization interrupted by user.")
    except Exception as e:
        print(f"Error during optimization: {e}")
        logging.error(f"Error during optimization: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())