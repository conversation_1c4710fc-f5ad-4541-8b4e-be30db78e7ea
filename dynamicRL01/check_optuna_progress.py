import optuna
import sys

def main():
    """Check the progress of the Optuna study."""
    try:
        # Load the study
        study_name = "tsngcl_optimization"
        storage_name = "sqlite:///optuna_study.db"
        
        study = optuna.load_study(
            study_name=study_name,
            storage=storage_name
        )
        
        # Print study statistics
        print(f"Study '{study_name}' loaded successfully")
        print(f"Number of completed trials: {len(study.trials)}")
        
        # Print best trial so far if any trials have completed
        if len(study.trials) > 0 and study.best_trial:
            print(f"Best trial so far: #{study.best_trial.number}")
            print(f"  Value: {study.best_trial.value}")
            print(f"  Params: {study.best_trial.params}")
            
            # Print all completed trials
            print("\nAll completed trials:")
            for i, trial in enumerate(study.trials):
                if trial.state == optuna.trial.TrialState.COMPLETE:
                    print(f"Trial #{trial.number}: Value = {trial.value}")
        else:
            print("No completed trials yet.")
            
    except Exception as e:
        print(f"Error checking study progress: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
