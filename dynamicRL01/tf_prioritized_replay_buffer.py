"""
Prioritized Experience Replay Buffer for TensorFlow Agents.

This module implements a prioritized experience replay buffer that inherits from
TFUniformReplayBuffer. It assigns priorities to experiences and samples them
based on these priorities, which can lead to more efficient learning.

Based on the paper "Prioritized Experience Replay" by <PERSON><PERSON><PERSON> et al.
(https://arxiv.org/abs/1511.05952)
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import collections
import tensorflow as tf
import numpy as np

from tf_agents.replay_buffers import tf_uniform_replay_buffer
from tf_agents.replay_buffers import table
from tf_agents.specs import tensor_spec
from tf_agents.utils import common
from tf_agents.utils import nest_utils

# Named tuple for returning buffer info with priorities
BufferInfo = collections.namedtuple('BufferInfo', ['ids', 'probabilities'])

class TFPrioritizedReplayBuffer(tf_uniform_replay_buffer.TFUniformReplayBuffer):
    """A TensorFlow prioritized experience replay buffer.

    This buffer extends TFUniformReplayBuffer to implement prioritized experience
    replay. It assigns priorities to experiences and samples them based on these
    priorities, which can lead to more efficient learning.

    The implementation uses a separate table to store priorities for each experience.
    New experiences are assigned the maximum priority observed so far to ensure
    they are sampled at least once.
    """

    def __init__(self,
                 data_spec,
                 batch_size,
                 max_length=1000,
                 alpha=1.0,
                 beta=0.5,
                 epsilon=1e-6,
                 scope='TFPrioritizedReplayBuffer',
                 device='cpu:*',
                 table_fn=table.Table,
                 dataset_drop_remainder=False,
                 dataset_window_shift=None,
                 stateful_dataset=False):
        """Creates a TFPrioritizedReplayBuffer.

        Args:
            data_spec: A TensorSpec or a list/tuple/nest of TensorSpecs describing a
                single item that can be stored in this buffer.
            batch_size: Batch dimension of tensors when adding to buffer.
            max_length: The maximum number of items that can be stored in a single
                batch segment of the buffer.
            alpha: The prioritization exponent (α). Controls how much prioritization
                is used, with α=0 corresponding to uniform sampling. Default: 1.0
            beta: The importance sampling exponent (β). Controls how much importance
                sampling is used to correct for the bias introduced by prioritized
                sampling. Default: 0.5
            epsilon: Small positive constant added to priorities to ensure non-zero
                sampling probability. Default: 1e-6
            scope: Scope prefix for variables and ops created by this class.
            device: A TensorFlow device to place the Variables and ops.
            table_fn: Function to create tables `table_fn(data_spec, capacity)` that
                can read/write nested tensors.
            dataset_drop_remainder: If `True`, then when calling
                `as_dataset` with arguments `single_deterministic_pass=True` and
                `sample_batch_size is not None`, the final batch will be dropped if it
                does not contain exactly `sample_batch_size` items.
            dataset_window_shift: Window shift used when calling `as_dataset` with
                arguments `single_deterministic_pass=True` and `num_steps is not None`.
            stateful_dataset: whether the dataset contains stateful ops or not.
        """
        # Initialize parent class
        super(TFPrioritizedReplayBuffer, self).__init__(
            data_spec=data_spec,
            batch_size=batch_size,
            max_length=max_length,
            scope=scope,
            device=device,
            table_fn=table_fn,
            dataset_drop_remainder=dataset_drop_remainder,
            dataset_window_shift=dataset_window_shift,
            stateful_dataset=stateful_dataset
        )

        # Store prioritization parameters
        self._alpha = alpha
        self._beta = beta
        self._epsilon = epsilon

        # Create priority spec
        self._prio_spec = tensor_spec.TensorSpec([], dtype=tf.float32, name='priority')

        # Create priority table
        with tf.device(self._device), tf.compat.v1.variable_scope(self._scope):
            self._priority_table = table_fn(self._prio_spec, self._capacity_value)

        # Initialize max priority to 1.0 to ensure all new experiences are sampled
        self._max_priority = tf.Variable(1.0, dtype=tf.float32, trainable=False)

    def _add_batch(self, items):
        """Adds a batch of items to the replay buffer with max priority.

        Args:
            items: A tensor or list/tuple/nest of tensors representing a batch of
                items to be added to the replay buffer. Each element of `items` must match
                the data_spec of this class. Should be shape [batch_size, data_spec, ...]

        Returns:
            An op that adds `items` to the replay buffer with maximum priority.
        """
        # Call parent class method to add items to the buffer
        with tf.device(self._device), tf.name_scope(self._scope):
            id_ = self._increment_last_id()
            write_rows = self._get_rows_for_id(id_)

            # Write data and ID
            write_id_op = self._id_table.write(write_rows, id_)
            write_data_op = self._data_table.write(write_rows, items)

            # Write max priority for new items to ensure they are sampled
            priorities = tf.fill([self._batch_size], self._max_priority)
            write_priority_op = self._priority_table.write(write_rows, priorities)

            return tf.group(write_id_op, write_data_op, write_priority_op)

    def _get_next(self, sample_batch_size=None, num_steps=None, time_stacked=True):
        """Returns an item or batch of items sampled based on priorities.

        Args:
            sample_batch_size: (Optional.) An optional batch_size to specify the
                number of items to return.
            num_steps: (Optional.) Optional way to specify that sub-episodes are
                desired.
            time_stacked: Bool, when true and num_steps > 1 get_next on the buffer
                would return the items stack on the time dimension.

        Returns:
            A 2 tuple, containing:
            - An item, sequence of items, or batch thereof sampled based on priorities.
            - BufferInfo NamedTuple, containing:
                - The items' ids.
                - The sampling probability of each item.
        """
        with tf.device(self._device), tf.name_scope(self._scope):
            with tf.name_scope('get_next'):
                # Get valid range of IDs
                min_val, max_val = self._valid_range_ids(
                    self._get_last_id(), self._max_length, num_steps)

                # Ensure buffer is not empty
                assert_nonempty = tf.compat.v1.assert_greater(
                    max_val,
                    min_val,
                    message='TFPrioritizedReplayBuffer is empty. Make sure to add items '
                            'before sampling the buffer.')

                with tf.control_dependencies([assert_nonempty]):
                    # Get all valid rows using TensorFlow operations
                    # Create a range of valid IDs
                    valid_ids = tf.range(min_val, max_val, dtype=tf.int64)

                    # Create batch offsets
                    batch_offsets = tf.range(self._batch_size, dtype=tf.int64) * self._max_length

                    # Create a mesh grid of batch offsets and valid IDs
                    # This creates all combinations of batch offsets and valid IDs
                    batch_mesh, id_mesh = tf.meshgrid(batch_offsets, valid_ids)

                    # Flatten the meshgrid results
                    batch_indices = tf.reshape(batch_mesh, [-1])
                    id_indices = tf.reshape(id_mesh, [-1])

                    # Combine batch offsets and IDs, then apply modulo to get valid rows
                    valid_rows = tf.math.floormod(batch_indices + id_indices, self._capacity)

                    # Read priorities for all valid rows
                    priorities = self._priority_table.read(valid_rows)

                    # Apply prioritization exponent (alpha)
                    priorities_alpha = tf.pow(priorities + self._epsilon, self._alpha)

                    # Normalize priorities to get probabilities
                    sum_priorities = tf.reduce_sum(priorities_alpha)
                    probabilities = priorities_alpha / sum_priorities

                    # Sample based on probabilities
                    indices = tf.random.categorical(
                        tf.math.log([probabilities]),
                        sample_batch_size or 1)
                    indices = tf.squeeze(indices, axis=0)

                    # Get the sampled rows
                    sampled_rows = tf.gather(valid_rows, indices)

                    # Get the sampled probabilities
                    sampled_probs = tf.gather(probabilities, indices)

                    # Read data from sampled rows
                    if num_steps is None:
                        # Single step case
                        data = self._data_table.read(sampled_rows)

                        # Create buffer info with IDs and probabilities
                        buffer_info = BufferInfo(
                            ids=sampled_rows,
                            probabilities=sampled_probs)

                        return data, buffer_info
                    else:
                        # Multi-step case
                        if time_stacked:
                            # Create time-stacked data
                            data_list = []
                            for step in range(num_steps):
                                step_rows = tf.math.mod(sampled_rows + step, self._capacity)
                                step_data = self._data_table.read(step_rows)
                                data_list.append(step_data)

                            # Stack along time dimension
                            data = tf.nest.map_structure(
                                lambda *tensors: tf.stack(tensors, axis=1),
                                *data_list)

                            # Create buffer info with IDs and probabilities
                            buffer_info = BufferInfo(
                                ids=sampled_rows,
                                probabilities=sampled_probs)

                            return data, buffer_info
                        else:
                            # Return list of data for each step
                            data_list = []
                            for step in range(num_steps):
                                step_rows = tf.math.mod(sampled_rows + step, self._capacity)
                                step_data = self._data_table.read(step_rows)
                                data_list.append(step_data)

                            # Create buffer info with IDs and probabilities
                            buffer_info = BufferInfo(
                                ids=sampled_rows,
                                probabilities=sampled_probs)

                            return tuple(data_list), buffer_info

    def update_priorities(self, ids, priorities):
        """Updates the priorities of the experiences with the given IDs.

        Args:
            ids: A tensor of IDs of the experiences to update.
            priorities: A tensor of new priorities for the experiences.

        Returns:
            An op that updates the priorities in the buffer.
        """
        with tf.device(self._device), tf.name_scope(self._scope):
            # Update max priority
            self._max_priority.assign(tf.maximum(self._max_priority, tf.reduce_max(priorities)))

            # Write new priorities
            return self._priority_table.write(ids, priorities)

    def get_beta(self):
        """Returns the current beta value for importance sampling."""
        return self._beta

    def set_beta(self, beta):
        """Sets the beta value for importance sampling.

        Args:
            beta: New beta value (between 0 and 1).
        """
        self._beta = beta

    def _valid_range_ids(self, last_id, max_length, num_steps=None):
        """Returns the [min_val, max_val) range of ids.

        When num_steps is provided, [min_val, max_val+num_steps) are also valid ids.

        Args:
            last_id: The last id added to the buffer.
            max_length: The max length of each batch segment in the buffer.
            num_steps: Optional way to specify that how many ids need to be valid.

        Returns:
            A tuple (min_id, max_id) for the range [min_id, max_id) of valid ids.
        """
        if num_steps is None:
            num_steps = tf.constant(1, tf.int64)

        min_id_not_full = tf.constant(0, dtype=tf.int64)
        max_id_not_full = tf.maximum(last_id + 1 - num_steps + 1, 0)

        min_id_full = last_id + 1 - max_length
        max_id_full = last_id + 1 - num_steps + 1

        return tf.cond(
            pred=last_id < max_length - 1,
            true_fn=lambda: (min_id_not_full, max_id_not_full),
            false_fn=lambda: (min_id_full, max_id_full)
        )
