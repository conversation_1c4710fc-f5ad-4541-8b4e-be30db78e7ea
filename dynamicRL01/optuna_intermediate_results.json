{"best_trial": {"number": 49, "value": 50154.00921016399, "params": {"learning_rate": 0.0005831605096373321, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.011423932540995442, "importance_ratio_clipping": 0.22775741315763118, "num_epochs": 5, "lambda_value": 0.9563059741267267, "discount_factor": 0.9655781441740209, "collect_steps_per_iteration": 200, "num_iterations": 20}, "user_attrs": {"avg_loss": 2750.82704755995, "avg_return": 31.30036625299189, "final_loss": 120.91204833984375, "final_performance_score": 15.650183126495945, "initial_loss": 22510.5078125, "learning_score": 50154.00921016399, "loss_improvement": 22389.595764160156, "loss_improvement_score": 223895.95764160156, "loss_stability": 0.00014832609504345033, "loss_stability_score": 0.0007416304752172517, "loss_trend": 0.9946286396847652, "max_return": 44.5102330327034, "min_loss": 63.513336181640625, "return_improvement": -14.325361764430998, "return_improvement_score": 0, "return_stability": 0.15188801277320413}}, "trials": [{"number": 0, "value": 48.3967842578888, "params": {"learning_rate": 0.0002143069485992712, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.03577650050031898, "importance_ratio_clipping": 0.19224107139980817, "num_epochs": 3, "lambda_value": 0.9223264841494542, "discount_factor": 0.9668472762915541, "collect_steps_per_iteration": 50, "num_iterations": 10}}, {"number": 1, "value": 49.221116721630096, "params": {"learning_rate": 0.0003760976298547943, "n_layers": 2, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.006901877141474935, "importance_ratio_clipping": 0.240084142464929, "num_epochs": 6, "lambda_value": 0.9602991143476494, "discount_factor": 0.9685196678195055, "collect_steps_per_iteration": 150, "num_iterations": 24}}, {"number": 2, "value": 45.723819476366046, "params": {"learning_rate": 4.66237773848255e-05, "n_layers": 1, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.015633954922382398, "importance_ratio_clipping": 0.2730414030768562, "num_epochs": 3, "lambda_value": 0.9261496869922882, "discount_factor": 0.9590067579247438, "collect_steps_per_iteration": 150, "num_iterations": 10}}, {"number": 3, "value": 50.0, "params": {"learning_rate": 0.0002445040545664239, "n_layers": 4, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.002032122137921108, "importance_ratio_clipping": 0.24254213388560356, "num_epochs": 6, "lambda_value": 0.9373397084258782, "discount_factor": 0.9702514255272104, "collect_steps_per_iteration": 150, "num_iterations": 20}}, {"number": 4, "value": 48.75201952457428, "params": {"learning_rate": 6.214336232493719e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.009880581891809934, "importance_ratio_clipping": 0.18096931731428356, "num_epochs": 6, "lambda_value": 0.9584989188689333, "discount_factor": 0.9577510496172815, "collect_steps_per_iteration": 100, "num_iterations": 18}}, {"number": 5, "value": 45.70875232815742, "params": {"learning_rate": 0.0006815593708570434, "n_layers": 3, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.005653579787432237, "importance_ratio_clipping": 0.2777257497848473, "num_epochs": 5, "lambda_value": 0.9653253734507159, "discount_factor": 0.962214857246153, "collect_steps_per_iteration": 200, "num_iterations": 21}}, {"number": 6, "value": 50.0, "params": {"learning_rate": 0.00011135435392405868, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.006551798127892554, "importance_ratio_clipping": 0.2612405962240325, "num_epochs": 7, "lambda_value": 0.9463233765788334, "discount_factor": 0.9513196688608128, "collect_steps_per_iteration": 150, "num_iterations": 23}}, {"number": 7, "value": 46.868446975946426, "params": {"learning_rate": 6.559789013110094e-05, "n_layers": 2, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.0253375686659558, "importance_ratio_clipping": 0.13729240652599237, "num_epochs": 7, "lambda_value": 0.969947900523109, "discount_factor": 0.9593949234505758, "collect_steps_per_iteration": 100, "num_iterations": 22}}, {"number": 8, "value": 48.087792778015135, "params": {"learning_rate": 0.0002277560066765563, "n_layers": 4, "layer_size": 64, "activation_fn": "tanh", "entropy_regularization": 0.02164334478170005, "importance_ratio_clipping": 0.23904756432665936, "num_epochs": 7, "lambda_value": 0.9578235164979749, "discount_factor": 0.9989193237314435, "collect_steps_per_iteration": 150, "num_iterations": 15}}, {"number": 9, "value": 48.51849289536476, "params": {"learning_rate": 0.00012221290499692483, "n_layers": 3, "layer_size": 128, "activation_fn": "relu", "entropy_regularization": 0.011949126938951485, "importance_ratio_clipping": 0.20156069065729165, "num_epochs": 8, "lambda_value": 0.930570220706831, "discount_factor": 0.9873765584934289, "collect_steps_per_iteration": 200, "num_iterations": 18}}, {"number": 10, "value": 49.489293813705444, "params": {"learning_rate": 1.2637678387583504e-05, "n_layers": 4, "layer_size": 128, "activation_fn": "tanh", "entropy_regularization": 0.001524171303367171, "importance_ratio_clipping": 0.12232295460784887, "num_epochs": 4, "lambda_value": 0.9379315518674913, "discount_factor": 0.9791188990062436, "collect_steps_per_iteration": 50, "num_iterations": 15}}, {"number": 11, "value": 47.3694447696209, "params": {"learning_rate": 2.0134890579140853e-05, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.00239546470928825, "importance_ratio_clipping": 0.2990258105187136, "num_epochs": 8, "lambda_value": 0.9454475922772732, "discount_factor": 0.9503245795631157, "collect_steps_per_iteration": 150, "num_iterations": 25}}, {"number": 12, "value": 50.0, "params": {"learning_rate": 0.0007558190078943895, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.0033214276726616122, "importance_ratio_clipping": 0.2420448175761632, "num_epochs": 5, "lambda_value": 0.9466275442129956, "discount_factor": 0.9785374921332083, "collect_steps_per_iteration": 150, "num_iterations": 21}}, {"number": 13, "value": 47.012977349758145, "params": {"learning_rate": 0.00013286673622029904, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.001020297436992068, "importance_ratio_clipping": 0.2202645477956163, "num_epochs": 6, "lambda_value": 0.9370518863245713, "discount_factor": 0.9503775116565794, "collect_steps_per_iteration": 150, "num_iterations": 23}}, {"number": 14, "value": 44.936912381649016, "params": {"learning_rate": 0.00035170220153145425, "n_layers": 4, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.003821643352241423, "importance_ratio_clipping": 0.26568761362931437, "num_epochs": 7, "lambda_value": 0.951586806395787, "discount_factor": 0.9878809243495915, "collect_steps_per_iteration": 150, "num_iterations": 20}}, {"number": 15, "value": 48.90638488531113, "params": {"learning_rate": 2.9637195075754193e-05, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.001917844495558409, "importance_ratio_clipping": 0.1584938914029581, "num_epochs": 2, "lambda_value": 0.9362566771778362, "discount_factor": 0.9720166108899003, "collect_steps_per_iteration": 150, "num_iterations": 15}}, {"number": 16, "value": 48.24055289030075, "params": {"learning_rate": 0.00018447991802588438, "n_layers": 1, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.004274114187004611, "importance_ratio_clipping": 0.21490394743341737, "num_epochs": 6, "lambda_value": 0.9508654952194237, "discount_factor": 0.9852657697411811, "collect_steps_per_iteration": 200, "num_iterations": 19}}, {"number": 17, "value": 48.429238486289975, "params": {"learning_rate": 0.00040865856397753915, "n_layers": 4, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.0012777110296664392, "importance_ratio_clipping": 0.2556673174536993, "num_epochs": 8, "lambda_value": 0.9407806159888045, "discount_factor": 0.9958343574673383, "collect_steps_per_iteration": 100, "num_iterations": 25}}, {"number": 18, "value": 50.0, "params": {"learning_rate": 7.347220735110478e-05, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.008638849322252819, "importance_ratio_clipping": 0.2953702008727056, "num_epochs": 4, "lambda_value": 0.928196571698851, "discount_factor": 0.9545870592731608, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 19, "value": 47.252757745981214, "params": {"learning_rate": 9.522355278675491e-05, "n_layers": 2, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.002339351661053157, "importance_ratio_clipping": 0.22239281877346664, "num_epochs": 7, "lambda_value": 0.9330176695923416, "discount_factor": 0.9674648836400926, "collect_steps_per_iteration": 150, "num_iterations": 16}}, {"number": 20, "value": 45.916925984621045, "params": {"learning_rate": 3.492293823266888e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.04756474537993449, "importance_ratio_clipping": 0.10309694073597313, "num_epochs": 5, "lambda_value": 0.9420674714982631, "discount_factor": 0.9759131873787813, "collect_steps_per_iteration": 150, "num_iterations": 12}}, {"number": 21, "value": 46.8226213991642, "params": {"learning_rate": 0.0008718067846838082, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.003386679356468025, "importance_ratio_clipping": 0.23934456261728762, "num_epochs": 5, "lambda_value": 0.9472812556593433, "discount_factor": 0.9816008514992383, "collect_steps_per_iteration": 150, "num_iterations": 21}}, {"number": 22, "value": 50.0, "params": {"learning_rate": 0.0005605155240452279, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.0029875085085030824, "importance_ratio_clipping": 0.25107889994234234, "num_epochs": 5, "lambda_value": 0.9529720991669351, "discount_factor": 0.9743810270834585, "collect_steps_per_iteration": 150, "num_iterations": 20}}, {"number": 23, "value": 44.151210218667984, "params": {"learning_rate": 0.000259883436051056, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.00533364595734662, "importance_ratio_clipping": 0.2790076475272329, "num_epochs": 6, "lambda_value": 0.9473926669209286, "discount_factor": 0.9817432527096187, "collect_steps_per_iteration": 150, "num_iterations": 22}}, {"number": 24, "value": 50.0, "params": {"learning_rate": 0.0001634417368275109, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.0019122992115472057, "importance_ratio_clipping": 0.25553280407261597, "num_epochs": 7, "lambda_value": 0.9420101047455584, "discount_factor": 0.9637429678641399, "collect_steps_per_iteration": 150, "num_iterations": 20}}, {"number": 25, "value": 42.18156883120537, "params": {"learning_rate": 0.0005251595597386907, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.004618014179781724, "importance_ratio_clipping": 0.23092835385127305, "num_epochs": 4, "lambda_value": 0.9545132838874097, "discount_factor": 0.9902408545139872, "collect_steps_per_iteration": 150, "num_iterations": 17}}, {"number": 26, "value": 41.95231803655624, "params": {"learning_rate": 0.000996439133505645, "n_layers": 4, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.002806126103984582, "importance_ratio_clipping": 0.20376648164253675, "num_epochs": 5, "lambda_value": 0.9337168796484872, "discount_factor": 0.9717492904905761, "collect_steps_per_iteration": 50, "num_iterations": 23}}, {"number": 28, "value": 22.023177840140118, "params": {"learning_rate": 0.00033782837740512704, "n_layers": 4, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.006734060801095921, "importance_ratio_clipping": 0.17649839770845574, "num_epochs": 6, "lambda_value": 0.943266107716389, "discount_factor": 0.977926965753222, "collect_steps_per_iteration": 200, "num_iterations": 21}, "user_attrs": {"avg_loss": 13236.818505859375, "avg_return": 29.418359891176227, "final_loss": 6943.15673828125, "loss_score": 7.554114747512032e-05, "loss_weight": 0.3, "max_return": 31.461358880996706, "min_loss": 5297.9921875, "return_weight": 0.7}}, {"number": 29, "value": 34.23302015915827, "params": {"learning_rate": 9.896250729204207e-05, "n_layers": 3, "layer_size": 256, "activation_fn": "relu", "entropy_regularization": 0.0016132342845292887, "importance_ratio_clipping": 0.2889087091032217, "num_epochs": 4, "lambda_value": 0.9491164934509676, "discount_factor": 0.9537441338832836, "collect_steps_per_iteration": 100, "num_iterations": 19}, "user_attrs": {"avg_loss": 4910.0550729851975, "avg_return": 31.76731536000967, "final_loss": 1839.439697265625, "loss_score": 0.0002036222329292975, "loss_weight": 0.3, "max_return": 48.9034418463707, "min_loss": 1425.004638671875, "return_weight": 0.7}}, {"number": 30, "value": 33.92720503442786, "params": {"learning_rate": 0.00024185844324538198, "n_layers": 2, "layer_size": 32, "activation_fn": "tanh", "entropy_regularization": 0.013394669306454788, "importance_ratio_clipping": 0.260777169844418, "num_epochs": 3, "lambda_value": 0.9391321433373249, "discount_factor": 0.9637843985961413, "collect_steps_per_iteration": 50, "num_iterations": 24}, "user_attrs": {"avg_loss": 6439.283807051809, "avg_return": 29.323548229187736, "final_loss": 2584.57080078125, "loss_score": 0.0001552726603298207, "loss_weight": 0.3, "max_return": 48.46677030920982, "min_loss": 1929.6829833984375, "return_weight": 0.7}}, {"number": 31, "value": 27.70679699083569, "params": {"learning_rate": 0.0006843692938157671, "n_layers": 2, "layer_size": 32, "activation_fn": "relu", "entropy_regularization": 0.003731501762668697, "importance_ratio_clipping": 0.24757654052986613, "num_epochs": 7, "lambda_value": 0.9255178234853041, "discount_factor": 0.9933772206577864, "collect_steps_per_iteration": 150, "num_iterations": 22}, "user_attrs": {"avg_loss": 32782.93520220588, "avg_return": 31.338939147856497, "final_loss": 26611.25, "loss_score": 3.050274452509028e-05, "loss_weight": 0.3, "max_return": 39.58100783228874, "min_loss": 16343.74609375, "return_weight": 0.7}}, {"number": 32, "value": 31.821353610824545, "params": {"learning_rate": 7.214131274447085e-05, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.008981291767321231, "importance_ratio_clipping": 0.29068054174881724, "num_epochs": 4, "lambda_value": 0.9293934703164505, "discount_factor": 0.9554868713651399, "collect_steps_per_iteration": 50, "num_iterations": 23}, "user_attrs": {"avg_loss": 27008.039950284092, "avg_return": 29.465353644165123, "final_loss": 19133.763671875, "loss_score": 3.702464070698972e-05, "loss_weight": 0.3, "max_return": 45.45891790986061, "min_loss": 13021.74609375, "return_weight": 0.7}}, {"number": 33, "value": 28.43096915735111, "params": {"learning_rate": 8.172869328247941e-05, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.008945834825923756, "importance_ratio_clipping": 0.26943763460428505, "num_epochs": 4, "lambda_value": 0.9206189752218366, "discount_factor": 0.9550207339732258, "collect_steps_per_iteration": 50, "num_iterations": 23}, "user_attrs": {"avg_loss": 7781.6120372953865, "avg_return": 30.28791029453278, "final_loss": 4022.34326171875, "loss_score": 0.00012849156494090382, "loss_weight": 0.3, "max_return": 40.61511954665184, "min_loss": 2942.4130859375, "return_weight": 0.7}}, {"number": 34, "value": 31.38911918716137, "params": {"learning_rate": 5.2523103187383665e-05, "n_layers": 1, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.0065160087094628324, "importance_ratio_clipping": 0.285812553249165, "num_epochs": 3, "lambda_value": 0.925029858883355, "discount_factor": 0.9688004149137621, "collect_steps_per_iteration": 50, "num_iterations": 24}, "user_attrs": {"avg_loss": 89085.859375, "avg_return": 33.40003715952238, "final_loss": 61332.3828125, "loss_score": 1.122500003946289e-05, "loss_weight": 0.3, "max_return": 44.841550731658934, "min_loss": 22773.9609375, "return_weight": 0.7}}, {"number": 35, "value": 26.910153467136, "params": {"learning_rate": 0.00014846501636202873, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.016173884101709248, "importance_ratio_clipping": 0.2327280516630735, "num_epochs": 2, "lambda_value": 0.9295335200970815, "discount_factor": 0.9611277132009056, "collect_steps_per_iteration": 50, "num_iterations": 19}, "user_attrs": {"avg_loss": 7298.984130859375, "avg_return": 32.25413237571716, "final_loss": 4474.826171875, "loss_score": 0.0001369865991588501, "loss_weight": 0.3, "max_return": 38.44248929619789, "min_loss": 3646.1044921875, "return_weight": 0.7}}, {"number": 36, "value": 30.819289145552684, "params": {"learning_rate": 3.8251706867855777e-05, "n_layers": 4, "layer_size": 64, "activation_fn": "elu", "entropy_regularization": 0.008516566203756869, "importance_ratio_clipping": 0.26936699765205885, "num_epochs": 5, "lambda_value": 0.9466945756897178, "discount_factor": 0.9527446113564823, "collect_steps_per_iteration": 150, "num_iterations": 21}, "user_attrs": {"avg_loss": 4579.158959960938, "avg_return": 29.242676383554944, "final_loss": 1973.29052734375, "loss_score": 0.00021833303357849583, "loss_weight": 0.3, "max_return": 44.02662020921707, "min_loss": 1486.8372802734375, "return_weight": 0.7}}, {"number": 37, "value": 32.37108310090966, "params": {"learning_rate": 0.0001174400061257445, "n_layers": 3, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.0055818225382486646, "importance_ratio_clipping": 0.187333804708117, "num_epochs": 6, "lambda_value": 0.9560973337589036, "discount_factor": 0.956602729599103, "collect_steps_per_iteration": 100, "num_iterations": 22}, "user_attrs": {"avg_loss": 15080.104902787642, "avg_return": 31.115823859247293, "final_loss": 3141.076171875, "loss_score": 6.630813898888514e-05, "loss_weight": 0.3, "max_return": 46.24412025213242, "min_loss": 2151.43505859375, "return_weight": 0.7}}, {"number": 38, "value": 35.00045716288353, "params": {"learning_rate": 0.00028562730017548657, "n_layers": 2, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.007633241999173119, "importance_ratio_clipping": 0.2974811530603715, "num_epochs": 5, "lambda_value": 0.9624624502909841, "discount_factor": 0.9602000621225169, "collect_steps_per_iteration": 50, "num_iterations": 25}, "user_attrs": {"avg_loss": 6561.2125244140625, "avg_return": 25.720636894089402, "final_loss": 1842.35107421875, "loss_score": 0.00015238762784344442, "loss_weight": 0.3, "max_return": 50.0, "min_loss": 1324.9444580078125, "return_weight": 0.7}}, {"number": 39, "value": 35.00037229103397, "params": {"learning_rate": 0.0001920416036876157, "n_layers": 4, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.011432576513939474, "importance_ratio_clipping": 0.27806435922704476, "num_epochs": 6, "lambda_value": 0.9340952274229405, "discount_factor": 0.9710203496795526, "collect_steps_per_iteration": 150, "num_iterations": 24}, "user_attrs": {"avg_loss": 8057.2117919921875, "avg_return": 27.242999719952206, "final_loss": 2212.0654296875, "loss_score": 0.00012409701132374624, "loss_weight": 0.3, "max_return": 50.0, "min_loss": 1595.6163330078125, "return_weight": 0.7}}, {"number": 40, "value": 27.6598284772936, "params": {"learning_rate": 5.9136767006615296e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.019371947606134383, "importance_ratio_clipping": 0.24630821855823493, "num_epochs": 4, "lambda_value": 0.9230777871733679, "discount_factor": 0.9586225541744486, "collect_steps_per_iteration": 200, "num_iterations": 17}, "user_attrs": {"avg_loss": 5610.955824908088, "avg_return": 31.620405256085924, "final_loss": 2782.659912109375, "loss_score": 0.0001781909963655813, "loss_weight": 0.3, "max_return": 39.513277006149295, "min_loss": 2384.490234375, "return_weight": 0.7}}, {"number": 41, "value": 33.89514314077815, "params": {"learning_rate": 0.00043605569156231574, "n_layers": 3, "layer_size": 64, "activation_fn": "relu", "entropy_regularization": 0.004726067705849615, "importance_ratio_clipping": 0.28023203057025586, "num_epochs": 3, "lambda_value": 0.9314626875389597, "discount_factor": 0.9651156346719506, "collect_steps_per_iteration": 150, "num_iterations": 12}, "user_attrs": {"avg_loss": 17022.179646809895, "avg_return": 36.73264324367046, "final_loss": 5697.95458984375, "loss_score": 5.874343223461181e-05, "loss_weight": 0.3, "max_return": 48.42138130068779, "min_loss": 5697.95458984375, "return_weight": 0.7}}, {"number": 42, "value": 34.311853163883285, "params": {"learning_rate": 0.0005875083341659765, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.002977721109146437, "importance_ratio_clipping": 0.2538263757138979, "num_epochs": 5, "lambda_value": 0.9534657284456335, "discount_factor": 0.974378569277098, "collect_steps_per_iteration": 150, "num_iterations": 20}, "user_attrs": {"avg_loss": 9974.45173187256, "avg_return": 27.01856566132104, "final_loss": 462.5218505859375, "loss_score": 0.0001002460867817044, "loss_weight": 0.3, "max_return": 49.01650346517563, "min_loss": 337.97027587890625, "return_weight": 0.7}}, {"number": 43, "value": 33.92574818033051, "params": {"learning_rate": 0.0007354458851054112, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.0024356492656359163, "importance_ratio_clipping": 0.20938936949479534, "num_epochs": 5, "lambda_value": 0.9441782562833982, "discount_factor": 0.9748125503966449, "collect_steps_per_iteration": 150, "num_iterations": 21}, "user_attrs": {"avg_loss": 10835.191105651855, "avg_return": 26.16939499896311, "final_loss": 303.2553405761719, "loss_score": 9.228334848011566e-05, "loss_weight": 0.3, "max_return": 48.464959043264386, "min_loss": 177.08322143554688, "return_weight": 0.7}}, {"number": 44, "value": 27.68897862827277, "params": {"learning_rate": 0.0004944360317243163, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.0032826277709304863, "importance_ratio_clipping": 0.2332871905352038, "num_epochs": 6, "lambda_value": 0.960673059334137, "discount_factor": 0.9817559197769473, "collect_steps_per_iteration": 150, "num_iterations": 18}, "user_attrs": {"avg_loss": 9877.759778340658, "avg_return": 29.51157425509559, "final_loss": 216.7794189453125, "loss_score": 0.00010122728180844283, "loss_weight": 0.3, "max_return": 39.555249923467635, "min_loss": 201.4203338623047, "return_weight": 0.7}}, {"number": 45, "value": 26.961712557446027, "params": {"learning_rate": 0.0008354955052394902, "n_layers": 3, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.02907389582975941, "importance_ratio_clipping": 0.24741663261165842, "num_epochs": 4, "lambda_value": 0.9504304047267216, "discount_factor": 0.9779829492007939, "collect_steps_per_iteration": 150, "num_iterations": 20}, "user_attrs": {"avg_loss": 9634.864349365234, "avg_return": 30.417947949469088, "final_loss": 744.08642578125, "loss_score": 0.00010377896198444048, "loss_weight": 0.3, "max_return": 38.516287457942965, "min_loss": 744.08642578125, "return_weight": 0.7}}, {"number": 46, "value": 30.26404597787349, "params": {"learning_rate": 0.0003337830496231773, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.001981759935773215, "importance_ratio_clipping": 0.22188641161525005, "num_epochs": 6, "lambda_value": 0.927568874437857, "discount_factor": 0.9518934333048799, "collect_steps_per_iteration": 150, "num_iterations": 22}, "user_attrs": {"avg_loss": 3235.3946422230115, "avg_return": 29.516451939804984, "final_loss": 1361.10107421875, "loss_score": 0.00030898580381814037, "loss_weight": 0.3, "max_return": 43.233027172088626, "min_loss": 1295.474853515625, "return_weight": 0.7}}, {"number": 47, "value": 34.6089130150905, "params": {"learning_rate": 2.3497971046482944e-05, "n_layers": 4, "layer_size": 128, "activation_fn": "elu", "entropy_regularization": 0.001318669286932321, "importance_ratio_clipping": 0.2629101734463099, "num_epochs": 8, "lambda_value": 0.9531177145868596, "discount_factor": 0.9845798016624258, "collect_steps_per_iteration": 100, "num_iterations": 19}, "user_attrs": {"avg_loss": 21298.448216488487, "avg_return": 27.78529701061725, "final_loss": 4293.2763671875, "loss_score": 4.6949573051656455e-05, "loss_weight": 0.3, "max_return": 49.44110309481621, "min_loss": 4293.2763671875, "return_weight": 0.7}}, {"number": 48, "value": 4.649481887075946, "params": {"learning_rate": 7.839055628172685e-05, "n_layers": 3, "layer_size": 32, "activation_fn": "elu", "entropy_regularization": 0.0027496579890282807, "importance_ratio_clipping": 0.19565252889443888, "num_epochs": 7, "lambda_value": 0.936053111193865, "discount_factor": 0.9697924655069655, "collect_steps_per_iteration": 150, "num_iterations": 23}, "user_attrs": {"avg_loss": 32103.468155570652, "avg_return": 45.0298697883884, "final_loss": 32527.234375, "final_performance_score": 22.5149348941942, "initial_loss": 23637.021484375, "learning_score": 4.649481887075946, "loss_improvement": -8890.212890625, "loss_improvement_score": 0, "loss_stability": 0.0002624991321763307, "loss_stability_score": 0.0013124956608816536, "loss_trend": -0.37611392351196954, "max_return": 47.22753445506096, "min_loss": 23637.021484375, "return_improvement": 6.529228496551518, "return_improvement_score": 13.058456993103036, "return_stability": 0.3471455975816697}}, {"number": 49, "value": 50154.00921016399, "params": {"learning_rate": 0.0005831605096373321, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.011423932540995442, "importance_ratio_clipping": 0.22775741315763118, "num_epochs": 5, "lambda_value": 0.9563059741267267, "discount_factor": 0.9655781441740209, "collect_steps_per_iteration": 200, "num_iterations": 20}, "user_attrs": {"avg_loss": 2750.82704755995, "avg_return": 31.30036625299189, "final_loss": 120.91204833984375, "final_performance_score": 15.650183126495945, "initial_loss": 22510.5078125, "learning_score": 50154.00921016399, "loss_improvement": 22389.595764160156, "loss_improvement_score": 223895.95764160156, "loss_stability": 0.00014832609504345033, "loss_stability_score": 0.0007416304752172517, "loss_trend": 0.9946286396847652, "max_return": 44.5102330327034, "min_loss": 63.513336181640625, "return_improvement": -14.325361764430998, "return_improvement_score": 0, "return_stability": 0.15188801277320413}}, {"number": 50, "value": 41830.642678201344, "params": {"learning_rate": 1.0972873665512283e-05, "n_layers": 3, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.010974766832084737, "importance_ratio_clipping": 0.2266081402724516, "num_epochs": 7, "lambda_value": 0.9648717339811809, "discount_factor": 0.9664057390750262, "collect_steps_per_iteration": 200, "num_iterations": 18}, "user_attrs": {"avg_loss": 6934.308132595486, "avg_return": 29.02206729153792, "final_loss": 3447.739990234375, "final_performance_score": 14.51103364576896, "initial_loss": 22121.58984375, "learning_score": 41830.642678201344, "loss_improvement": 18673.849853515625, "loss_improvement_score": 186738.49853515625, "loss_stability": 0.00014196446961877462, "loss_stability_score": 0.0007098223480938731, "loss_trend": 0.8441459219438306, "max_return": 39.22400670647621, "min_loss": 2671.7353515625, "return_improvement": -2.3864629626274088, "return_improvement_score": 0, "return_stability": 0.16336620403273985}}, {"number": 51, "value": 37305.84877335853, "params": {"learning_rate": 1.348036682608768e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.010934468369015163, "importance_ratio_clipping": 0.22653969215835812, "num_epochs": 8, "lambda_value": 0.968319352264334, "discount_factor": 0.9656289950717809, "collect_steps_per_iteration": 200, "num_iterations": 18}, "user_attrs": {"avg_loss": 5789.01702202691, "avg_return": 30.74182128542, "final_loss": 4778.25390625, "final_performance_score": 15.37091064271, "initial_loss": 21432.07421875, "learning_score": 37305.84877335853, "loss_improvement": 16653.8203125, "loss_improvement_score": 166538.203125, "loss_stability": 0.00020868668644130888, "loss_stability_score": 0.0010434334322065443, "loss_trend": 0.7770512616987062, "max_return": 36.57203253507614, "min_loss": 3580.3056640625, "return_improvement": -4.975361642241481, "return_improvement_score": 0, "return_stability": 0.22252421308504422}}, {"number": 52, "value": 44223.476952628436, "params": {"learning_rate": 1.0917441208680812e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.011262312848425287, "importance_ratio_clipping": 0.22706858727199086, "num_epochs": 8, "lambda_value": 0.9699823803493779, "discount_factor": 0.9661280422507985, "collect_steps_per_iteration": 200, "num_iterations": 18}, "user_attrs": {"avg_loss": 6910.713026258681, "avg_return": 26.045689495238992, "final_loss": 2129.83740234375, "final_performance_score": 13.022844747619496, "initial_loss": 21871.97265625, "learning_score": 44223.476952628436, "loss_improvement": 19742.13525390625, "loss_improvement_score": 197421.3525390625, "loss_stability": 0.00011592791359150638, "loss_stability_score": 0.0005796395679575319, "loss_trend": 0.9026225281177306, "max_return": 50.0, "min_loss": 2014.0958251953125, "return_improvement": -24.590661028027533, "return_improvement_score": 0, "return_stability": 0.0829057803889946}}, {"number": 53, "value": 44551.82857193553, "params": {"learning_rate": 1.1650785987527469e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.01497546807374419, "importance_ratio_clipping": 0.225897866003539, "num_epochs": 8, "lambda_value": 0.9697830591851021, "discount_factor": 0.9660184661181894, "collect_steps_per_iteration": 200, "num_iterations": 16}, "user_attrs": {"avg_loss": 8387.888870239258, "avg_return": 25.92017428446561, "final_loss": 1866.75732421875, "final_performance_score": 12.960087142232805, "initial_loss": 21755.48046875, "learning_score": 44551.82857193553, "loss_improvement": 19888.72314453125, "loss_improvement_score": 198887.2314453125, "loss_stability": 0.00014440282726210524, "loss_stability_score": 0.0007220141363105262, "loss_trend": 0.914193698139639, "max_return": 44.37176214456558, "min_loss": 1866.75732421875, "return_improvement": -13.913310217857362, "return_improvement_score": 0, "return_stability": 0.07103637053746227}}, {"number": 54, "value": 44945.98241659188, "params": {"learning_rate": 1.0373216545087577e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.01064154462657693, "importance_ratio_clipping": 0.21321594497285457, "num_epochs": 8, "lambda_value": 0.969716535027389, "discount_factor": 0.9663677405515182, "collect_steps_per_iteration": 200, "num_iterations": 16}, "user_attrs": {"avg_loss": 8284.617395737592, "avg_return": 27.690500137023626, "final_loss": 2028.1590576171875, "final_performance_score": 13.845250068511813, "initial_loss": 22092.810546875, "learning_score": 44945.98241659188, "loss_improvement": 20064.651489257812, "loss_improvement_score": 200646.51489257812, "loss_stability": 0.00014222969079170084, "loss_stability_score": 0.0007111484539585042, "loss_trend": 0.9081982324831882, "max_return": 42.38485996723175, "min_loss": 2028.1590576171875, "return_improvement": -14.042651265859604, "return_improvement_score": 0, "return_stability": 0.10036953966437641}}, {"number": 55, "value": 45238.413578205545, "params": {"learning_rate": 1.0235705438598157e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.014591314515136561, "importance_ratio_clipping": 0.21230345174714288, "num_epochs": 8, "lambda_value": 0.9699552874857229, "discount_factor": 0.9658926370171562, "collect_steps_per_iteration": 200, "num_iterations": 16}, "user_attrs": {"avg_loss": 9076.802604675293, "avg_return": 25.103382519676238, "final_loss": 1469.1566162109375, "final_performance_score": 12.551691259838119, "initial_loss": 21664.40625, "learning_score": 45238.413578205545, "loss_improvement": 20195.249633789062, "loss_improvement_score": 201952.49633789062, "loss_stability": 0.00010080752927292867, "loss_stability_score": 0.0005040376463646434, "loss_trend": 0.9321856967018914, "max_return": 48.60064546465874, "min_loss": 1469.1566162109375, "return_improvement": -25.978866586089136, "return_improvement_score": 0, "return_stability": 0.06337679379913293}}, {"number": 56, "value": 49182.58776962923, "params": {"learning_rate": 1.0709840216641864e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.01407204823575941, "importance_ratio_clipping": 0.21247714354081818, "num_epochs": 8, "lambda_value": 0.9665734044619643, "discount_factor": 0.9671262756075988, "collect_steps_per_iteration": 200, "num_iterations": 14}, "user_attrs": {"avg_loss": 8045.889208112444, "avg_return": 29.468501449057037, "final_loss": 831.1417236328125, "final_performance_score": 14.734250724528518, "initial_loss": 22787.1015625, "learning_score": 49182.58776962923, "loss_improvement": 21955.959838867188, "loss_improvement_score": 219559.59838867188, "loss_stability": 9.55462024814111e-05, "loss_stability_score": 0.0004777310124070555, "loss_trend": 0.9635257814007994, "max_return": 48.64828844070435, "min_loss": 831.1417236328125, "return_improvement": -29.37959256768227, "return_improvement_score": 0, "return_stability": 0.07271412891268865}}, {"number": 57, "value": 38552.1071422903, "params": {"learning_rate": 1.5932114777961057e-05, "n_layers": 4, "layer_size": 256, "activation_fn": "tanh", "entropy_regularization": 0.01490726946784399, "importance_ratio_clipping": 0.20727106030447598, "num_epochs": 8, "lambda_value": 0.9691769268758926, "discount_factor": 0.9624442721004361, "collect_steps_per_iteration": 200, "num_iterations": 14}, "user_attrs": {"avg_loss": 6685.474827357701, "avg_return": 29.3221917409982, "final_loss": 1682.4224853515625, "final_performance_score": 14.6610958704991, "initial_loss": 18892.634765625, "learning_score": 38552.1071422903, "loss_improvement": 17210.212280273438, "loss_improvement_score": 172102.12280273438, "loss_stability": 0.0001829011983269015, "loss_stability_score": 0.0009145059916345075, "loss_trend": 0.9109482342604369, "max_return": 41.89405596852303, "min_loss": 1682.4224853515625, "return_improvement": -12.786508229374888, "return_improvement_score": 0, "return_stability": 0.11766856629109963}}]}