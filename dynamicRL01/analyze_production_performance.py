"""
Analyze the performance of the production policy.

This script loads and analyzes the statistics from production runs,
generating visualizations and summary metrics to evaluate performance.
"""

import json
import os
import sys
import glob
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def load_stats_files(directory="production_stats"):
    """Load all statistics files from the given directory."""
    if not os.path.exists(directory):
        print(f"Error: Directory {directory} not found.")
        return []
    
    # Find all JSON files in the directory
    stats_files = glob.glob(os.path.join(directory, "*.json"))
    
    if not stats_files:
        print(f"No statistics files found in {directory}.")
        return []
    
    # Load each file
    all_stats = []
    for file_path in stats_files:
        try:
            with open(file_path, 'r') as f:
                stats = json.load(f)
                # Add the filename for reference
                stats['file_path'] = file_path
                all_stats.append(stats)
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
    
    # Sort by timestamp if available
    all_stats.sort(key=lambda x: x.get('timestamp', ''))
    
    return all_stats

def create_summary(all_stats):
    """Create a summary of all production runs."""
    if not all_stats:
        return "No statistics available."
    
    # Calculate summary statistics
    total_runs = len(all_stats)
    total_steps = sum(stats.get('total_steps', 0) for stats in all_stats)
    total_runtime = sum(stats.get('total_runtime_seconds', 0) for stats in all_stats)
    total_episodes = sum(stats.get('episodes_completed', 0) for stats in all_stats)
    
    # Calculate averages
    avg_steps_per_run = total_steps / total_runs if total_runs > 0 else 0
    avg_runtime_per_run = total_runtime / total_runs if total_runs > 0 else 0
    avg_episodes_per_run = total_episodes / total_runs if total_runs > 0 else 0
    
    # Collect all episode rewards
    all_episode_rewards = []
    for stats in all_stats:
        if 'episode_rewards' in stats and stats['episode_rewards']:
            all_episode_rewards.extend(stats['episode_rewards'])
    
    # Calculate reward statistics
    if all_episode_rewards:
        avg_episode_reward = sum(all_episode_rewards) / len(all_episode_rewards)
        min_episode_reward = min(all_episode_rewards)
        max_episode_reward = max(all_episode_rewards)
        median_episode_reward = np.median(all_episode_rewards)
    else:
        avg_episode_reward = min_episode_reward = max_episode_reward = median_episode_reward = 0
    
    # Format the summary
    summary = "Production Performance Summary\n"
    summary += "============================\n\n"
    summary += f"Total Runs: {total_runs}\n"
    summary += f"Total Steps: {total_steps}\n"
    summary += f"Total Runtime: {total_runtime:.2f} seconds ({total_runtime/3600:.2f} hours)\n"
    summary += f"Total Episodes Completed: {total_episodes}\n\n"
    
    summary += f"Average Steps per Run: {avg_steps_per_run:.2f}\n"
    summary += f"Average Runtime per Run: {avg_runtime_per_run:.2f} seconds\n"
    summary += f"Average Episodes per Run: {avg_episodes_per_run:.2f}\n\n"
    
    summary += "Episode Reward Statistics:\n"
    summary += f"  Average: {avg_episode_reward:.4f}\n"
    summary += f"  Minimum: {min_episode_reward:.4f}\n"
    summary += f"  Maximum: {max_episode_reward:.4f}\n"
    summary += f"  Median: {median_episode_reward:.4f}\n"
    
    return summary

def create_visualizations(all_stats, output_dir="production_stats"):
    """Create visualizations of production performance."""
    if not all_stats:
        print("No statistics available for visualization.")
        return
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Prepare data for plots
    timestamps = []
    avg_rewards = []
    steps_per_second = []
    episode_lengths = []
    
    for stats in all_stats:
        # Convert timestamp to datetime if available
        if 'timestamp' in stats:
            try:
                dt = datetime.strptime(stats['timestamp'], "%Y-%m-%d %H:%M:%S")
                timestamps.append(dt)
                avg_rewards.append(stats.get('average_episode_reward', 0))
                steps_per_second.append(stats.get('steps_per_second', 0))
            except:
                pass
        
        # Collect episode lengths if available
        if 'episode_rewards' in stats and 'total_steps' in stats and 'episodes_completed' in stats:
            if stats['episodes_completed'] > 0:
                avg_episode_length = stats['total_steps'] / stats['episodes_completed']
                episode_lengths.append(avg_episode_length)
    
    # Plot 1: Average Reward Over Time
    if timestamps and avg_rewards:
        plt.figure(figsize=(10, 6))
        plt.plot(timestamps, avg_rewards, 'o-', linewidth=2)
        plt.title('Average Episode Reward Over Time')
        plt.xlabel('Date')
        plt.ylabel('Average Reward')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'reward_over_time.png'))
        plt.close()
    
    # Plot 2: Performance (Steps/Second) Over Time
    if timestamps and steps_per_second:
        plt.figure(figsize=(10, 6))
        plt.plot(timestamps, steps_per_second, 'o-', linewidth=2)
        plt.title('Performance (Steps/Second) Over Time')
        plt.xlabel('Date')
        plt.ylabel('Steps per Second')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'performance_over_time.png'))
        plt.close()
    
    # Plot 3: Distribution of Episode Rewards
    all_episode_rewards = []
    for stats in all_stats:
        if 'episode_rewards' in stats and stats['episode_rewards']:
            all_episode_rewards.extend(stats['episode_rewards'])
    
    if all_episode_rewards:
        plt.figure(figsize=(10, 6))
        plt.hist(all_episode_rewards, bins=30, alpha=0.7)
        plt.title('Distribution of Episode Rewards')
        plt.xlabel('Episode Reward')
        plt.ylabel('Frequency')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'reward_distribution.png'))
        plt.close()
    
    # Plot 4: Average Episode Length
    if episode_lengths:
        plt.figure(figsize=(10, 6))
        plt.bar(range(len(episode_lengths)), episode_lengths)
        plt.title('Average Episode Length by Run')
        plt.xlabel('Run Index')
        plt.ylabel('Average Steps per Episode')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'episode_length.png'))
        plt.close()
    
    print(f"Visualizations saved to {output_dir}")

def main():
    """Analyze production performance."""
    print("Analyzing production performance...")
    
    # Get the stats directory
    stats_dir = input("Enter the path to the production stats directory (default: production_stats): ") or "production_stats"
    
    # Load all statistics files
    all_stats = load_stats_files(stats_dir)
    
    if not all_stats:
        print("No statistics available for analysis.")
        return 1
    
    print(f"Loaded {len(all_stats)} statistics files.")
    
    # Create summary
    summary = create_summary(all_stats)
    print("\n" + summary)
    
    # Save summary to file
    summary_file = os.path.join(stats_dir, "performance_summary.txt")
    with open(summary_file, 'w') as f:
        f.write(summary)
    
    print(f"Summary saved to {summary_file}")
    
    # Create visualizations
    create_visualizations(all_stats, stats_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
