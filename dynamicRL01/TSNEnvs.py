import logging  # Add logging module
import zmq
import json
import time
import threading
import numpy as np
from collections import defaultdict
import re
from threading import Lock
import os
import tensorflow as tf
from tf_agents.agents.ppo import ppo_agent
from tf_agents.environments import py_environment
from tf_agents.specs import array_spec
from tf_agents.trajectories import time_step as ts
from tf_agents.environments import tf_py_environment
from tf_agents.networks import actor_distribution_network, value_network
from tf_agents.networks import nest_map
from tf_agents.networks import encoding_network
from tf_agents.networks import network
from tf_agents.trajectories import trajectory
from tf_agents.replay_buffers import tf_uniform_replay_buffer
from tf_agents.utils import common
from tf_agents.drivers import driver, dynamic_episode_driver, dynamic_step_driver
from tf_agents.utils import nest_utils

# Configure logging
logging.basicConfig(level=logging.DEBUG, filename='app.log', 
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='a')

class TSNGCLEnvironment(py_environment.PyEnvironment):
    def __init__(self, port=5555):
        super(TSNGCLEnvironment, self).__init__()
        logging.info("Initializing TSN GCL Environment...")

        # Initialize ZeroMQ context and socket
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.ROUTER)
        self.socket.bind(f"tcp://*:{port}")
        self.socket.setsockopt(zmq.SNDHWM, 10000)
        self.socket.setsockopt(zmq.RCVHWM, 10000)
        logging.debug(f"ZeroMQ socket bound to port {port}")

        # Define expected queues (3 switches, 3 queues each)
        self.expected_queues = [
            "S0.eth[2].macLayer.queue.queue[0]",  # Network control
            "S0.eth[2].macLayer.queue.queue[1]",  # Video
            "S0.eth[2].macLayer.queue.queue[2]",  # Best effort
            "S1.eth[1].macLayer.queue.queue[0]",  # Network control
            "S1.eth[1].macLayer.queue.queue[1]",  # Video
            "S1.eth[1].macLayer.queue.queue[2]",  # Best effort
            "S2.eth[1].macLayer.queue.queue[0]",  # Network control
            "S2.eth[1].macLayer.queue.queue[1]",  # Video
            "S2.eth[1].macLayer.queue.queue[2]",  # Best effort
        ]
        # logging.debug(f"Expected queues: {self.expected_queues}")

        self.num_queues = len(self.expected_queues)
        self.valid_queue_pattern = re.compile(
            r"S0\.eth\[2\]\.macLayer\.queue\.queue\[[0-2]\]$|S[1-2]\.eth\[1\]\.macLayer\.queue\.queue\[[0-2]\]$"
        )

        # State management
        self.queue_states = {queue_id: {} for queue_id in self.expected_queues}
        self.state_batch = defaultdict(list)
        self.start_time = defaultdict(lambda: None)
        self.lock = Lock()
        self.running = True
        self.current_episode_step = 0
        self.max_episode_steps = 1000

        # GCL configuration (initial values)
        self.gcl_config = np.array([4.0, 3.0, 2.0, 1.0])  # [NC, Video, BE, All closed]
        # logging.debug(f"Initial GCL configuration: {self.gcl_config}")

        # Define action and observation specs, changed minimum 1 to 0
        self._action_spec = array_spec.BoundedArraySpec(
            shape=(4,), dtype=np.float32, minimum=0.0, maximum=10, name='action'
        )

        # Using a flat observation space instead of a dictionary
        # This simplifies the network architecture
        self._observation_spec = array_spec.BoundedArraySpec(
            shape=(self.num_queues * 3 + 4,),  # {queue_lengths + occupancy + rates}/queue + gcl
            dtype=np.float32,
            minimum=0.0,
            maximum=1000.0,
            name='observation'
        )
        self.identiy_map = {}
        logging.debug("Action and observation specs defined.")

        # Start background thread for collecting data
        self.state_thread = threading.Thread(target=self._state_monitor)
        self.state_thread.daemon = True
        self.state_thread.start()
        logging.info("State monitor thread started.")

        # Add data activity tracking
        self.last_data_received_time = None
        self.data_active = False  # Start in inactive state until data is received
        self.data_check_interval = 1.0  # Check every 1 second for more responsive pausing

        # Start data activity monitor thread
        self.activity_thread = threading.Thread(target=self._monitor_data_activity)
        self.activity_thread.daemon = True
        self.activity_thread.start()

    def _monitor_data_activity(self):
        """Monitor if data is being received from simulation."""
        while self.running:
            if self.last_data_received_time is not None:
                time_since_last_data = time.time() - self.last_data_received_time

                # If no data for 3 seconds, consider simulation inactive
                if time_since_last_data > 3.0:
                    if self.data_active:
                        logging.info("Simulation data stream inactive. Agent paused.")
                        # print("\n*** Simulation data stream inactive. Agent paused. ***\n")
                        self.data_active = False
                else:
                    if not self.data_active:
                        logging.info("Simulation data stream active. Agent resumed.")
                        # print("\n*** Simulation data stream active. Agent resumed. ***\n")
                        self.data_active = True
            else:
                # No data has been received yet
                if self.data_active:
                    logging.info("No initial data received. Agent paused.")
                    # print("\n*** Waiting for initial data from omnet++. Agent paused. ***\n")
                    self.data_active = False

            time.sleep(self.data_check_interval)

    def action_spec(self):
        return self._action_spec

    def observation_spec(self):
        return self._observation_spec

    def _reset(self):
        """Reset the environment state."""
        logging.info("Resetting environment state...")
        self.current_episode_step = 0
        self.gcl_config = np.array([4.0, 3.0, 2.0, 1.0])
        observation = self._get_flattened_observation()
        logging.debug(f"Environment reset. Initial observation: {observation}")
        return ts.restart(observation)

    def _get_flattened_observation(self):
        """Convert the structured observation to a flat array."""
        logging.debug("Generating flattened observation...")
        queue_lengths = np.zeros(self.num_queues, dtype=np.float32)
        queue_occupancy = np.zeros(self.num_queues, dtype=np.float32)
        arrival_rates = np.zeros(self.num_queues, dtype=np.float32)

        with self.lock:
            for i, queue_id in enumerate(self.expected_queues):
                if queue_id in self.queue_states and self.queue_states[queue_id]:
                    #removed caping in queue length, queue occupancy, and arrival rates
                    # queue_lengths[i] = min(self.queue_states[queue_id].get('num_packets', 0), 100)
                    # queue_occupancy[i] = min(self.queue_states[queue_id].get('queue_occupancy', 0), 1.0)
                    # arrival_rates[i] = min(self.queue_states[queue_id].get('packetArrivalRate', 0), 1000)
                    queue_lengths[i] = self.queue_states[queue_id].get('num_packets')
                    queue_occupancy[i] = self.queue_states[queue_id].get('queue_occupancy')
                    arrival_rates[i] = self.queue_states[queue_id].get('packetArrivalRate')
                logging.debug(f"Queue {queue_id} state: {self.queue_states}")
        # removed scaled_gcl /10 parameter
        scaled_gcl = self.gcl_config
        observation = np.concatenate([queue_lengths, queue_occupancy, arrival_rates, scaled_gcl]).astype(np.float32)
        logging.debug(f"Flattened observation: {observation}")
        return observation

    def _step(self, action):
        """Execute one time step within the environment."""
        logging.info(f"Executing step {self.current_episode_step + 1} with action: {action}")

        # Check if data is active before proceeding
        if not self.data_active:
            logging.info("No data from omnet++. Agent paused.")
            # Return the current observation with zero reward, but don't update state
            observation = self._get_flattened_observation()
            # Sleep to avoid busy waiting
            time.sleep(0.5)
            return ts.transition(observation, reward=0.0, discount=0.99)

        if self.current_episode_step >= self.max_episode_steps:
            logging.info("Max episode steps reached. Resetting environment.")
            return self.reset()

        self.current_episode_step += 1
        # removed the action scaling
        # scaled_action = np.clip((action + 1) * 2.5, 0.5, 10.0)
        # self.gcl_config = scaled_action
        self.gcl_config = action
        logging.debug(f"Updated GCL configuration: {self.gcl_config}")

        for queue_id in self.expected_queues:
            self._send_action_to_omnet(queue_id, self.gcl_config)

        time.sleep(0.01)
        observation = self._get_flattened_observation()
        reward = self._calculate_reward(observation)
        logging.debug(f"Step reward: {reward}")

        if self.current_episode_step >= self.max_episode_steps:
            logging.info("Episode terminated.")
            return ts.termination(observation, reward)
        else:
            return ts.transition(observation, reward, discount=0.99)

    def _calculate_reward(self, observation):
        """Calculate reward based on flattened observation."""
        logging.debug("Calculating reward...")
        n = self.num_queues
        queue_lengths = observation[:n]
        queue_occupancy = observation[n:2*n]
        arrival_rates = observation[2*n:3*n]

        occupancy_penalty = -np.mean(queue_occupancy) * 2
        handling_reward = np.mean(arrival_rates) / 1000.0
        queue_type_weights = np.array([3.0, 2.0, 1.0] * 3)
        queue_imbalance = np.abs(queue_lengths - np.mean(queue_lengths))
        imbalance_penalty = -np.sum(queue_imbalance * queue_type_weights) / 100.0

        total_reward = occupancy_penalty + handling_reward + imbalance_penalty
        logging.debug(f"Reward components - Occupancy penalty: {occupancy_penalty}, Handling reward: {handling_reward}, Imbalance penalty: {imbalance_penalty}, Total reward: {total_reward}")
        return float(total_reward)

    def _state_monitor(self):
        """Background thread to monitor and collect state from OMNeT++."""
        logging.info("Starting state monitor...")
        poller = zmq.Poller()
        poller.register(self.socket, zmq.POLLIN)
        # logging.debug("Received data from omnet++: ", self.socket.recv_multipart())

        while self.running:
            try:
                # Use a shorter timeout for more responsive detection of data inactivity
                events = dict(poller.poll(timeout=50))
                if self.socket in events and events[self.socket] == zmq.POLLIN:
                    # logging.debug("Received data from omnet++: ", self.socket.recv_multipart())
                    identity, message = self.socket.recv_multipart()
                    # Update the last data received time
                    self.last_data_received_time = time.time()
                    # Check if the received message is empty and warn the user that the message is empty
                    if not message:
                        logging.warning("Received an empty message from omnet++.")
                        continue
                    # log the message and the identity
                    # logging.debug(f"Received message from omnet++: {message.decode()}")
                    # logging.debug(f"Received message from omnet++ with identity: {identity}")
                    data = json.loads(message.decode())
                    # queue_id_raw = identity.decode()
                    if 'queue_id' in data:
                        queue_id_raw = data['queue_id']
                        if "rlTest01." in queue_id_raw:
                            queue_id = queue_id_raw.split("rlTest01.")[-1]
                        else:
                            queue_id = queue_id_raw
                        logging.debug(f"Received message: {data} from omnet++ with queue_id: {queue_id}")
                    self.identiy_map[queue_id] = identity
                    #log the queue ID
                    # logging.debug(f"Received data for queue: {queue_id}")

                    # Validate queue ID
                    if not self.valid_queue_pattern.match(queue_id) or queue_id not in self.expected_queues:
                        logging.warning(f"Invalid queue ID received: {queue_id}")
                        continue

                    if 'state' in data and len(data['state']) >= 5:
                        logging.debug(f"Received state: {data['state']} from omnet++ with queue_id: {queue_id}")
                        current_time_ms = round(float(data['state'][4]) * 1000, 2)
                        with self.lock:
                            # self.queue_states[queue_id] = {
                            #     "num_packets": float(data['state'][0]),
                            #     "queue_occupancy": float(data['state'][1]),
                            #     "packetArrivalRate": float(data['state'][2]),
                            #     "deadline": float(data['state'][3]),
                            #     "existing_gcl": np.array(data['state'][4]),
                            #     "current_sim_time": current_time_ms
                            # }
                            self.queue_states[queue_id] = {
                                "num_packets": float(data['state'][0]),
                                "queue_occupancy": float(data['state'][1]),
                                "packetArrivalRate": float(data['state'][2]),
                                "deadline": float(data['state'][3]),
                                "current_sim_time": current_time_ms
                            }
                            logging.debug(f"Updated queue states: {self.queue_states}")
                            if self.start_time.get(queue_id) is None or (current_time_ms - self.start_time[queue_id]) >= 2:
                                if self.state_batch.get(queue_id):
                                    self._handle_batch_data(queue_id, self.state_batch[queue_id])
                                self.state_batch[queue_id] = []
                                self.start_time[queue_id] = current_time_ms
                            self.state_batch[queue_id].append(self.queue_states[queue_id])
            except Exception as e:
                logging.error(f"Error in state monitor: {e}")

    def _handle_batch_data(self, queue_id, batch_data):
        """Process batch data for a queue."""
        if not batch_data:
            return
        avg_num_packets = np.mean([d["num_packets"] for d in batch_data])
        avg_queue_occupancy = np.mean([d["queue_occupancy"] for d in batch_data])
        avg_packet_arrival_rate = np.mean([d["packetArrivalRate"] for d in batch_data])
        self.queue_states[queue_id].update({
            "num_packets": avg_num_packets,
            "queue_occupancy": avg_queue_occupancy,
            "packetArrivalRate": avg_packet_arrival_rate
        })
        logging.debug(f"Processed batch data for queue {queue_id}: {self.queue_states[queue_id]}")

    def _send_action_to_omnet(self, queue_id, action):
        """Send action to OMNeT++ simulation."""
        try:
            with self.lock:
                # queue_id = "rlTest01." + queue_id
                #change queue id to identity
                # self.socket.send_multipart([
                #     queue_id.encode(),
                #     b'',
                #     json.dumps({"action": action.tolist()}).encode()
                # ])
                # before seding, check if the queue_id is in the identity map
                if queue_id not in self.identiy_map:
                    logging.warning(f"Queue ID not found in identity map: {queue_id}")
                    return
                client_id = self.identiy_map[queue_id]
                logging.debug(f"Sending action to OMNeT++ for queue {queue_id} with identity {client_id}: {action}")
                # debug the identity_map
                # logging.debug(f"Identity map: {self.identiy_map}")
                self.socket.send_multipart([
                    client_id,
                    b'',
                    json.dumps({"action": action.tolist()}).encode()
                ])
            # logging.debug(f"Action sent to OMNeT++ for queue {queue_id}: {action}")
        except Exception as e:
            logging.error(f"Error sending action to OMNeT++: {e}")

    def close(self):
        """Close the environment."""
        self.running = False
        if self.state_thread.is_alive():
            self.state_thread.join(timeout=1.0)
        if self.activity_thread.is_alive():
            self.activity_thread.join(timeout=1.0)
        self.socket.close()
        self.context.term()
        super(TSNGCLEnvironment, self).close()
        # """Close the environment."""
        # logging.info("Closing environment...")
        # self.running = False
        # if self.state_thread.is_alive():
        #     self.state_thread.join(timeout=1.0)
        # self.socket.close()
        # self.context.term()
        # super(TSNGCLEnvironment, self).close()


def create_ppo_agent(env):
    """Create a PPO agent for the TSN GCL environment."""
    # Convert to TF environment
    tf_env = tf_py_environment.TFPyEnvironment(env)

    # Create the actor network
    actor_net = actor_distribution_network.ActorDistributionNetwork(
        tf_env.observation_spec(),
        tf_env.action_spec(),
        fc_layer_params=(64, 64),
        activation_fn=tf.keras.activations.relu
    )

    # Create the value network
    value_net = value_network.ValueNetwork(
        tf_env.observation_spec(),
        fc_layer_params=(64, 64),
        activation_fn=tf.keras.activations.relu
    )

    # Create the optimizer
    optimizer = tf.keras.optimizers.Adam(learning_rate=1e-4)

    # Create the PPO agent
    agent = ppo_agent.PPOAgent(
        tf_env.time_step_spec(),
        tf_env.action_spec(),
        actor_net=actor_net,
        value_net=value_net,
        optimizer=optimizer,
        entropy_regularization=0.01,
        importance_ratio_clipping=0.2,
        normalize_observations=True,
        normalize_rewards=True,
        use_gae=True,
        num_epochs=3,
        train_step_counter=tf.Variable(0)
    )

    agent.initialize()
    return agent, tf_env


class TSNGCLTrainer:
    def __init__(self, env, agent, tf_env, num_iterations=1000, collect_steps_per_iteration=100):
        self.env = env
        self.agent = agent
        self.tf_env = tf_env
        self.num_iterations = num_iterations
        self.collect_steps_per_iteration = collect_steps_per_iteration

        # Create a replay buffer
        self.replay_buffer = tf_uniform_replay_buffer.TFUniformReplayBuffer(
            data_spec=agent.collect_data_spec,
            batch_size=tf_env.batch_size,
            max_length=10000
        )

        # Create a driver for collecting experience
        self.collect_driver = dynamic_step_driver.DynamicStepDriver(
            self.tf_env,
            self.agent.collect_policy,
            observers=[self.replay_buffer.add_batch],
            num_steps=collect_steps_per_iteration
        )

    def collect_data(self):
        """Collect data using the collect driver."""
        # Only collect data if the environment is receiving data
        if self.env.data_active:
            self.collect_driver.run()
        else:
            logging.info("Skipping data collection as data stream is inactive")

    def train(self):
        """Train the agent using collected data."""
        # Skip training if data is not active
        if not self.env.data_active:
            logging.info("Skipping training as data stream is inactive")
            # Return a dummy loss object
            class DummyLoss:
                def __init__(self):
                    self.loss = 0.0
            return DummyLoss()

        # Get dataset from replay buffer
        dataset = self.replay_buffer.as_dataset(
            num_parallel_calls=3,
            sample_batch_size=64,
            num_steps=2
        ).prefetch(3)

        # Train the agent
        iterator = iter(dataset)
        experience, _ = next(iterator)
        return self.agent.train(experience)

    def run_training(self):
        """Run the training loop."""
        try:
            returns = []
            iteration = 0
            consecutive_inactive_checks = 0
            max_inactive_checks = 30  # After this many checks, we'll print a waiting message

            while iteration < self.num_iterations:
                # Check if simulation is active
                if not self.env.data_active:
                    consecutive_inactive_checks += 1

                    # Only print waiting message periodically to avoid spamming
                    if consecutive_inactive_checks % max_inactive_checks == 0:
                        print(f"Waiting for data from omnet++ for {consecutive_inactive_checks * 0.5:.1f} seconds...")
                        print("The agent is paused until data is received.")

                    # Sleep briefly to avoid busy waiting
                    time.sleep(0.5)
                    continue

                # Reset counter when data becomes active
                consecutive_inactive_checks = 0

                # Collect data
                self.collect_data()

                # Train the agent
                train_loss = self.train()

                # Evaluation
                if iteration % 2 == 0:
                    avg_return = self.compute_avg_return(num_episodes=3)
                    returns.append(avg_return)
                    print(f'Iteration {iteration}: Average Return = {np.mean(avg_return):.2f}, Loss = {np.mean(train_loss.loss):.4f}')

                iteration += 1

            return returns
        except KeyboardInterrupt:
            print("Training interrupted by user.")
        finally:
            self.env.close()
        # """Run the training loop."""
        # try:
        #     returns = []
        #     for iteration in range(self.num_iterations):
        #         # Collect data
        #         self.collect_data()

        #         # Train the agent
        #         train_loss = self.train()

        #         # Evaluation
        #         if iteration % 2 == 0:
        #             avg_return = self.compute_avg_return(num_episodes=3)
        #             returns.append(avg_return)
        #             print(f'Iteration {iteration}: Average Return = {np.mean(avg_return):.2f}, Loss = {np.mean(train_loss.loss):.4f}')


        #     return returns
        # except KeyboardInterrupt:
        #     print("Training interrupted by user.")
        # finally:
        #     self.env.close()

    def compute_avg_return(self, num_episodes=5):
        """Compute average return for evaluation."""
        # Skip evaluation if data is not active
        if not self.env.data_active:
            logging.info("Skipping evaluation as data stream is inactive")
            return 0.0

        total_return = 0.0
        for _ in range(num_episodes):
            time_step = self.tf_env.reset()
            episode_return = 0.0

            while not time_step.is_last():
                # Check if data became inactive during evaluation
                if not self.env.data_active:
                    logging.info("Data stream became inactive during evaluation, stopping")
                    return 0.0

                action_step = self.agent.policy.action(time_step)
                time_step = self.tf_env.step(action_step.action)
                episode_return += time_step.reward
            #log total return
            logging.info(f"Episode return: {episode_return}")
            logging.info(f"Total return: {total_return}")
            total_return += episode_return

        avg_return = total_return / num_episodes
        return avg_return


def debug_simple_agent():
    """A debugging function that uses a PPO agent to compute actions"""
    # Initialize the environment
    env = TSNGCLEnvironment(port=5555)
    
    try:
        print("Starting PPO-based debug agent...")
        print("Initializing PPO agent...")
        
        # Create the PPO agent
        agent, tf_env = create_ppo_agent(env)
        
        # Initialize metrics tracking
        rewards = []
        episode_count = 0
        step_count = 0
        
        print("Waiting for data from OMNeT++...")
        
        # Get initial time step
        time_step = tf_env.reset()
        
        while True:
            # Check if data is active
            if env.data_active:
                # Use the agent's policy to select an action
                action_step = agent.policy.action(time_step)
                action = action_step.action.numpy()[0]  # Extract action from batch
                
                # Apply the action and get next time step
                next_time_step = tf_env.step(action_step.action)
                
                # Get reward from the time step
                reward = next_time_step.reward.numpy()[0]
                rewards.append(reward)
                
                # Print state and action information
                step_count += 1
                logging.debug(f"\n--- Step {step_count} ---")
                logging.debug(f"Action: {action}")
                logging.debug(f"Reward: {reward}")
                
                # Check if episode ended
                if next_time_step.is_last():
                    episode_count += 1
                    avg_reward = sum(rewards[-env.max_episode_steps:]) / min(len(rewards), env.max_episode_steps)
                    logging.debug(f"\n=== Episode {episode_count} completed ===")
                    logging.debug(f"Average reward: {avg_reward:.4f}")
                    logging.debug("=================================")
                    
                    # Reset for next episode
                    time_step = tf_env.reset()
                else:
                    time_step = next_time_step
                
                # Sleep to avoid overwhelming the system
                time.sleep(0.5)
            else:
                logging.info("Waiting for data from OMNeT++...")
                time.sleep(1.0)
                
    except KeyboardInterrupt:
        print("\nDebug session interrupted by user.")
        if rewards:
            logging.debug(f"Final statistics:")
            logging.debug(f"Total episodes: {episode_count}")
            logging.debug(f"Total steps: {step_count}")
            logging.debug(f"Average reward: {sum(rewards)/len(rewards):.4f}")
            logging.debug(f"Best reward: {max(rewards):.4f}")
            logging.debug(f"Worst reward: {min(rewards):.4f}")
    finally:
        env.close()
        logging.info("Environment closed.")

# For debugging, replace the main block with this:
if __name__ == "__main__":
    # Initialize the environment
    env = TSNGCLEnvironment(port=5555)
    
    try:
        print("Starting TSNGCLTrainer...")
        
        # Create the PPO agent
        agent, tf_env = create_ppo_agent(env)
        
        # Create and run the trainer
        trainer = TSNGCLTrainer(env, agent, tf_env, num_iterations=1000, collect_steps_per_iteration=100)
        returns = trainer.run_training()
        
        print("Training completed.")
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user.")
    finally:
        env.close()
        logging.info("Environment closed.")
