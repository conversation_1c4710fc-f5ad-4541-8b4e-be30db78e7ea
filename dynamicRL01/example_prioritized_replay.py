"""
Example demonstrating how to use the TFPrioritizedReplayBuffer with the TSNGCLEnvironment.

This example shows how to replace the TFUniformReplayBuffer with the TFPrioritizedReplayBuffer
in the TSNGCLTrainer class.
"""

import logging
import tensorflow as tf
from revamped_new import TSNGCLEnvironment, create_ppo_agent
from tf_prioritized_replay_buffer import TFPrioritizedReplayBuffer
from tf_agents.drivers import dynamic_step_driver

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    filename='prioritized_replay.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

class TSNGCLTrainerWithPER:
    """TSNGCLTrainer with Prioritized Experience Replay."""
    
    def __init__(self, env, agent, tf_env, num_iterations=30, collect_steps_per_iteration=150,
                 alpha=0.6, beta_start=0.4, beta_end=1.0, epsilon=1e-6):
        """Creates a TSNGCLTrainer with Prioritized Experience Replay.
        
        Args:
            env: The environment instance.
            agent: The agent instance.
            tf_env: The TensorFlow environment wrapper.
            num_iterations: Number of iterations to train.
            collect_steps_per_iteration: Number of steps to collect per iteration.
            alpha: The prioritization exponent (α). Controls how much prioritization
                is used, with α=0 corresponding to uniform sampling. Default: 0.6
            beta_start: Initial value of β for importance sampling. Default: 0.4
            beta_end: Final value of β for importance sampling. Default: 1.0
            epsilon: Small positive constant added to priorities. Default: 1e-6
        """
        self.env = env
        self.agent = agent
        self.tf_env = tf_env
        self.num_iterations = num_iterations
        self.collect_steps_per_iteration = collect_steps_per_iteration
        
        # Initialize last_real_loss and last_avg_return
        self.last_real_loss = None
        self.last_avg_return = None
        
        # Early stopping parameters
        self.best_loss = float('inf')
        self.best_return = float('-inf')
        self.patience = 5  # Number of iterations to wait for improvement
        self.patience_counter = 0
        self.min_delta_loss = 0.01  # Minimum change in loss to be considered an improvement
        self.min_delta_return = 0.1  # Minimum change in return to be considered an improvement
        
        # Prioritized Experience Replay parameters
        self.alpha = alpha
        self.beta_start = beta_start
        self.beta_end = beta_end
        self.epsilon = epsilon
        
        # Create a prioritized replay buffer
        self.replay_buffer = TFPrioritizedReplayBuffer(
            data_spec=agent.collect_data_spec,
            batch_size=tf_env.batch_size,
            max_length=10000,
            alpha=alpha,
            beta=beta_start,
            epsilon=epsilon
        )
        
        # Create a driver for collecting experience
        self.collect_driver = dynamic_step_driver.DynamicStepDriver(
            self.tf_env,
            self.agent.collect_policy,
            observers=[self.replay_buffer.add_batch],
            num_steps=collect_steps_per_iteration
        )
    
    def collect_data(self):
        """Collect data from the environment."""
        logging.info("Collecting data...")
        
        # Check if data is active
        if not self.env.data_active:
            logging.info("No data active from OMNeT++, waiting...")
            return False
        
        try:
            # Run the collect driver
            self.collect_driver.run()
            logging.info(f"Collected data, buffer now has {self.replay_buffer.num_frames()} frames")
            return True
        except Exception as e:
            logging.error(f"Error collecting data: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return False
    
    def train_step(self, iteration):
        """Perform a single training step.
        
        Args:
            iteration: Current iteration number.
            
        Returns:
            The training loss or None if training failed.
        """
        logging.info(f"Training step for iteration {iteration}")
        
        # Check if replay buffer has enough data
        if self.replay_buffer.num_frames() < 64:  # Minimum batch size
            logging.info("Not enough data in replay buffer for training, collecting more data")
            
            # Try to collect more data if data is active
            if not self.collect_data():
                logging.info("Failed to collect data, skipping training step")
                return None
            
            # If still not enough data, wait for more
            if self.replay_buffer.num_frames() < 64:
                logging.info("Still not enough data after collection attempt. Waiting for more data.")
                return None
        
        try:
            # Update beta for importance sampling (anneal from beta_start to beta_end)
            progress = min(1.0, iteration / self.num_iterations)
            current_beta = self.beta_start + progress * (self.beta_end - self.beta_start)
            self.replay_buffer.set_beta(current_beta)
            
            # Get dataset from replay buffer
            dataset = self.replay_buffer.as_dataset(
                num_parallel_calls=3,
                sample_batch_size=min(64, self.replay_buffer.num_frames()),
                num_steps=2
            ).prefetch(3)
            
            # Train the agent
            iterator = iter(dataset)
            experience, buffer_info = next(iterator)
            
            # Get the importance sampling weights
            # w_i = (N * P(i))^(-β)
            N = self.replay_buffer.num_frames()
            probs = buffer_info.probabilities
            importance_weights = tf.pow(N * probs, -current_beta)
            
            # Normalize weights to have a max of 1
            importance_weights = importance_weights / tf.reduce_max(importance_weights)
            
            # Train with importance sampling weights
            train_loss = self.agent.train(experience, weights=importance_weights)
            
            # Update priorities based on TD error (loss)
            td_errors = tf.abs(train_loss.extra.td_loss)
            new_priorities = td_errors + self.epsilon
            self.replay_buffer.update_priorities(buffer_info.ids, new_priorities)
            
            # Store the real loss for future reference
            self.last_real_loss = train_loss
            
            return train_loss
        except Exception as e:
            logging.error(f"Error during training: {e}")
            import traceback
            logging.error(traceback.format_exc())
            
            # Return None to indicate training failed
            logging.error("Training failed due to an error. Waiting for more data.")
            return None
    
    def run_training(self):
        """Run the training loop.
        
        Returns:
            A list of average returns from each iteration.
        """
        logging.info("Starting training with prioritized experience replay...")
        
        returns = []
        
        for i in range(self.num_iterations):
            logging.info(f"Starting iteration {i}")
            
            # Collect data
            if not self.collect_data():
                logging.info(f"No data available for iteration {i}, skipping")
                continue
            
            # Train the agent
            train_loss = self.train_step(i)
            
            if train_loss is None:
                logging.info(f"Training failed for iteration {i}, skipping")
                continue
            
            # Log training progress
            logging.info(f"Iteration {i}: Loss = {train_loss.loss}")
            
            # Evaluate the agent
            avg_return = self.evaluate()
            returns.append(avg_return)
            
            # Check for early stopping
            if self.check_early_stopping(train_loss.loss, avg_return):
                logging.info(f"Early stopping triggered at iteration {i}")
                break
        
        return returns
    
    def evaluate(self):
        """Evaluate the agent's performance.
        
        Returns:
            The average return from evaluation episodes.
        """
        # Simple evaluation: run one episode and return the total reward
        time_step = self.tf_env.reset()
        total_return = 0.0
        
        while not time_step.is_last():
            action_step = self.agent.policy.action(time_step)
            time_step = self.tf_env.step(action_step.action)
            total_return += time_step.reward
        
        logging.info(f"Evaluation return: {total_return}")
        self.last_avg_return = total_return
        
        return total_return
    
    def check_early_stopping(self, current_loss, current_return):
        """Check if training should be stopped early.
        
        Args:
            current_loss: The current training loss.
            current_return: The current evaluation return.
            
        Returns:
            True if training should be stopped, False otherwise.
        """
        should_stop = False
        
        # Check if loss improved
        if current_loss < self.best_loss - self.min_delta_loss:
            self.best_loss = current_loss
            self.patience_counter = 0
        # Check if return improved
        elif current_return > self.best_return + self.min_delta_return:
            self.best_return = current_return
            self.patience_counter = 0
        else:
            self.patience_counter += 1
            
        # Check if patience exceeded
        if self.patience_counter >= self.patience:
            should_stop = True
            
        return should_stop


def main():
    """Run training with prioritized experience replay."""
    print("Starting training with prioritized experience replay")
    
    # Create environment
    try:
        env = TSNGCLEnvironment(port=5555)
        print("Created TSNGCLEnvironment")
    except Exception as e:
        print(f"Error creating environment: {e}")
        return 1
    
    # Create agent
    try:
        agent, tf_env = create_ppo_agent(env)
        print("Created PPO agent")
    except Exception as e:
        print(f"Error creating agent: {e}")
        return 1
    
    # Create trainer with prioritized experience replay
    trainer = TSNGCLTrainerWithPER(
        env=env,
        agent=agent,
        tf_env=tf_env,
        num_iterations=30,
        collect_steps_per_iteration=150,
        alpha=0.6,  # Prioritization exponent
        beta_start=0.4,  # Initial importance sampling correction
        beta_end=1.0,  # Final importance sampling correction
        epsilon=1e-6  # Small constant to ensure non-zero priority
    )
    
    # Run training
    print("Starting training...")
    returns = trainer.run_training()
    
    # Print results
    if returns and len(returns) > 0:
        print(f"Training completed with final return: {returns[-1]}")
        print(f"Best return during training: {max(returns)}")
    
    return 0


if __name__ == "__main__":
    main()
