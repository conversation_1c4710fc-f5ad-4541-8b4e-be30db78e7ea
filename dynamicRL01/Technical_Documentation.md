# Technical Documentation: dynamicRL01 Project

## Overview

The dynamicRL01 project implements a reinforcement learning (RL) solution for optimizing Time-Sensitive Networking (TSN) Gate Control Lists (GCL) using Proximal Policy Optimization (PPO). The system interfaces with an OMNeT++ simulation environment through ZeroMQ for real-time communication, allowing the RL agent to learn optimal gate scheduling policies for different traffic classes.

## System Architecture

### Communication Layer

The project uses ZeroMQ (ZMQ) for bidirectional communication between the RL agent and the OMNeT++ simulation:

- **Protocol**: ROUTER socket pattern for handling multiple clients
- **Data Format**: JSON messages for state and action exchange
- **Connection Management**: Automatic reconnection and error handling mechanisms
- **Data Freshness**: Simulation time-based filtering to reject stale data

### Environment

The core environment is implemented in `TSNGCLEnvironment` class, which:

1. Interfaces with OMNeT++ via ZMQ
2. Processes network state information
3. Computes rewards based on queue metrics
4. Sends GCL configurations back to the simulation

#### Key Environment Components:

- **Observation Space**: Flattened array containing:
  - Queue lengths for each traffic class
  - Queue occupancy percentages
  - Packet arrival rates
  - Current GCL configuration

- **Action Space**: 4-dimensional continuous space representing:
  - Gate open time for Network Control (NC) traffic
  - Gate open time for Video traffic
  - Gate open time for Best Effort (BE) traffic
  - Guard band time

- **Reward Function**: Composite reward based on:
  - Occupancy penalty (lower queue occupancy is better)
  - Handling reward (higher throughput is better)
  - Imbalance penalty (balanced queues are better)
  - Priority-weighted components (higher priority traffic gets higher weight)

## Reinforcement Learning Implementation

### PPO Agent

The project uses Proximal Policy Optimization (PPO), a state-of-the-art policy gradient method:

- **Network Architecture**:
  - Actor network: Produces action distributions for GCL configuration
  - Value network: Estimates state values for advantage computation
  - Configurable hidden layers (typically 2-3 layers with 64-256 neurons)
  - ReLU activation function (configurable to ELU or tanh)

- **PPO-Specific Parameters**:
  - Learning rate: Typically 5e-5 to 1e-4 (tunable)
  - Entropy regularization: 0.01-0.05 for exploration
  - Importance ratio clipping: 0.2 (standard PPO clipping)
  - GAE parameter (λ): 0.95
  - Discount factor (γ): 0.99
  - Training epochs: 3-5 per batch

### Training Process

The training process is managed by the `TSNGCLTrainer` class, which:

1. Collects experience using the agent's policy
2. Stores transitions in a replay buffer
3. Periodically trains the agent on collected data
4. Evaluates performance and implements early stopping
5. Saves the best policy for production use

## Hyperparameter Optimization

The project uses Optuna for hyperparameter tuning:

- **Tunable Parameters**:
  - Learning rate
  - Network architecture (number of layers)
  - Activation functions
  - Entropy regularization
  - Importance ratio clipping
  - Number of training epochs
  - Dropout rate

- **Optimization Process**:
  - Objective function evaluates agent performance
  - Pruning stops unpromising trials early
  - Results are stored in SQLite database
  - Visualization tools for parameter importance analysis

## Production Deployment

### Policy Saving

The trained policy is saved using TensorFlow's SavedModel format:

1. `run_best_optuna.py` trains an agent with the best hyperparameters
2. `save_best_policy.py` extracts and saves just the policy for production use
3. Metadata about the policy is stored alongside the model

### Policy Execution

The `run_trained_policy.py` script implements a production-ready policy runner that:

1. Loads a saved policy model
2. Establishes ZMQ connection with OMNeT++
3. Implements data batching to reduce noise
4. Applies data freshness checks using simulation time
5. Monitors connection health and attempts recovery if needed
6. Provides detailed logging and diagnostics

## Key Features and Optimizations

1. **Data Batching**: Collects observations over time intervals before computing actions
2. **Simulation Time Awareness**: Uses OMNeT++ simulation time for synchronization
3. **Data Freshness Filtering**: Rejects outdated data from the ZMQ buffer
4. **Connection Monitoring**: Automatically attempts recovery if data flow stops
5. **Early Stopping**: Prevents overfitting during training
6. **Robust Error Handling**: Graceful recovery from communication failures

## Performance Considerations

- **ZMQ Socket Configuration**: High-water marks set to 10000 for send/receive operations
- **Timeout Handling**: Configurable timeouts for data reception
- **Resource Management**: Proper cleanup of ZMQ resources
- **Batch Processing**: Configurable batch intervals for action computation
- **Action Rate Limiting**: Prevents overwhelming the simulation with too many actions

## Conclusion

The dynamicRL01 project provides a complete solution for optimizing TSN Gate Control Lists using reinforcement learning. The implementation balances theoretical RL concepts with practical engineering considerations for real-time communication, data processing, and production deployment.
