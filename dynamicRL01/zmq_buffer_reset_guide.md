# ZMQ Buffer Reset Guide for `run_trained_policy.py`

## Overview

The enhanced `run_trained_policy.py` now includes **comprehensive ZMQ buffer reset functionality** to ensure that no buffered/stale data from previous runs is used when the script starts. This prevents the agent from processing outdated messages that may have accumulated in the ZMQ buffer.

## 🔄 **Automatic Buffer Reset Features**

### **1. Startup Buffer Reset**
- **When**: Automatically triggered every time `run_trained_policy.py` is started
- **What**: Comprehensive 5-step reset process
- **Result**: All buffered messages are cleared before the agent begins processing

### **2. Pre-Connection Buffer Reset**
- **When**: Before each connection attempt to OMNeT++
- **What**: Final buffer drain to ensure clean connection
- **Result**: No residual messages from previous connection attempts

### **3. Runtime Buffer Reset**
- **When**: Available as a method on the PolicyRunner class
- **What**: Can be called during execution to clear accumulated messages
- **Result**: Fresh data processing without restart

## 🛠️ **Reset Process Details**

### **Comprehensive ZMQ Reset (`comprehensive_zmq_reset()`)**

**5-Step Process:**

#### **Step 1: Context Termination**
```
1️⃣  Terminating existing ZMQ contexts...
   ✅ ZMQ context terminated
```
- Terminates any existing ZMQ contexts
- Ensures clean state for new connections

#### **Step 2: Internal State Reset**
```
2️⃣  Resetting ZMQ internal state...
   ✅ ZMQ internal state reset
```
- Clears ZMQ internal flags and variables
- Resets context instance references

#### **Step 3: Garbage Collection**
```
3️⃣  Forcing garbage collection...
   ✅ Garbage collection completed
```
- Forces Python garbage collection
- Cleans up any remaining ZMQ objects

#### **Step 4: Buffer Draining**
```
4️⃣  Clearing ZMQ message buffer...
🔄 Resetting ZMQ buffer on port 5555...
📡 Temporary socket bound to port 5555 for buffer clearing
🧹 Draining buffered messages...
  📨 Cleared message 1: 3 frames
  📨 Cleared message 2: 3 frames
  ...
✅ Buffer clearing complete: 15 messages removed
```
- Creates temporary socket on the target port
- Drains all available messages with timeout
- Performs aggressive additional clearing

#### **Step 5: Final Cleanup**
```
5️⃣  Final cleanup wait...
   ✅ Cleanup wait completed
```
- Waits for all cleanup operations to complete
- Ensures system is ready for fresh connections

## 📊 **Buffer Reset Output**

### **Successful Reset Example**
```
============================================================
🔄 COMPREHENSIVE ZMQ RESET
============================================================
1️⃣  Terminating existing ZMQ contexts...
   ✅ ZMQ context terminated
2️⃣  Resetting ZMQ internal state...
   ✅ ZMQ internal state reset
3️⃣  Forcing garbage collection...
   ✅ Garbage collection completed
4️⃣  Clearing ZMQ message buffer...
🔄 Resetting ZMQ buffer on port 5555...
📡 Temporary socket bound to port 5555 for buffer clearing
🧹 Draining buffered messages...
  📨 Cleared message 1: 3 frames
  📨 Cleared message 2: 3 frames
  📨 Cleared message 3: 3 frames
🔧 Performing additional buffer clearing...
  📨 Cleared 2 additional messages
✅ Buffer clearing complete: 5 messages removed
🔌 Temporary socket closed
🔌 Temporary context terminated
5️⃣  Final cleanup wait...
   ✅ Cleanup wait completed

✅ COMPREHENSIVE ZMQ RESET COMPLETE
   📊 Total messages cleared: 5
   🔌 Port 5555 is ready for fresh connections
============================================================
```

### **Clean Buffer Example**
```
✅ Successfully cleared 0 buffered messages
✅ No buffered messages found - buffer is clean
```

## 🎯 **When Buffer Reset Occurs**

### **1. Script Startup**
```bash
python run_trained_policy.py
```
**Triggers:**
- Comprehensive ZMQ reset after port selection
- Final buffer reset before PolicyRunner creation

### **2. Connection Attempts**
**Triggers:**
- Additional reset before each retry attempt
- Final buffer drain before creating PolicyRunner

### **3. Manual Reset (Runtime)**
```python
# During execution, if needed
runner.reset_buffer()
```

## ⚙️ **Configuration Options**

### **Buffer Reset Parameters**
- **`timeout_ms`**: Timeout for each message receive (default: 100ms)
- **`max_drain_time`**: Maximum time to spend draining (default: 5 seconds)
- **`additional_attempts`**: Extra clearing attempts (default: 10)

### **Customization**
The reset functions can be customized by modifying:
- `reset_zmq_buffer(port, timeout_ms)`
- `comprehensive_zmq_reset(port)`

## 🔍 **Troubleshooting**

### **Port In Use Warning**
```
⚠️  Port 5555 is in use - this is expected if OMNeT++ is running
   Buffer clearing may be limited, but will continue anyway
```
**Meaning**: OMNeT++ is already using the port (normal)
**Action**: Script continues normally

### **Buffer Reset Errors**
```
❌ ZMQ error during buffer clearing: [Error details]
```
**Meaning**: Technical issue during buffer clearing
**Action**: Check logs for details, script continues

### **No Messages Cleared**
```
✅ Buffer confirmed clean
```
**Meaning**: No buffered messages found (good!)
**Action**: Normal operation continues

## 🚀 **Benefits**

### **1. Fresh Data Processing**
- ✅ **No stale data** from previous runs
- ✅ **Current simulation state** only
- ✅ **Accurate decision making** based on fresh information

### **2. Consistent Behavior**
- ✅ **Predictable startup** behavior
- ✅ **Reproducible results** across runs
- ✅ **Reliable performance** metrics

### **3. Debugging Support**
- ✅ **Clear separation** between runs
- ✅ **Known starting state** for troubleshooting
- ✅ **Detailed logging** of reset operations

### **4. Production Readiness**
- ✅ **Robust initialization** process
- ✅ **Error handling** for edge cases
- ✅ **Automatic recovery** from buffer issues

## 📝 **Usage Examples**

### **Basic Usage**
```bash
python run_trained_policy.py
# Automatic buffer reset occurs
# Fresh data processing begins
```

### **Multiple Runs**
```bash
# Run 1
python run_trained_policy.py
# ... agent runs with fresh data

# Run 2 (later)
python run_trained_policy.py
# Buffer automatically cleared from Run 1
# Fresh data processing begins again
```

### **Manual Buffer Reset**
```python
# During execution, if buffer issues suspected
runner.reset_buffer()
# Continues with fresh data
```

## 🎯 **Conclusion**

The ZMQ buffer reset functionality ensures that `run_trained_policy.py` always starts with a clean slate, processing only fresh data from OMNeT++. This eliminates potential issues with stale buffered messages and provides consistent, reliable behavior across multiple runs.

**Key Benefits:**
- 🔄 **Automatic** buffer clearing on every startup
- 📊 **Detailed** feedback on messages cleared
- 🛡️ **Robust** error handling and recovery
- 🎯 **Fresh** data processing guaranteed
- 🔧 **Manual** reset capability for runtime use

Your agent will now always work with the most current data from OMNeT++! 🚀
