# Production Deployment Guide for TSN GCL Policy

This guide explains how to deploy and use the trained TSN GCL policy in a production environment.

## Overview

The production deployment process consists of three main steps:

1. **Save the trained policy**: Extract just the policy from the trained agent
2. **Run the policy in production**: Use the saved policy to make decisions in a production environment
3. **Analyze performance**: Evaluate how well the policy is performing in production

## Prerequisites

- A trained policy using `run_best_optuna.py`
- OMNeT++ environment set up and ready to connect
- Python 3.6+ with TensorFlow 2.x installed

## Step 1: Save the Trained Policy

After training your agent with `run_best_optuna.py`, you need to save just the policy for production use.

```bash
python save_best_policy.py
```

This script will:
- Load the best parameters from Optuna optimization
- Create a PPO agent with those parameters
- Save just the policy using TF-Agents PolicySaver
- Store the policy in the `saved_policies/production_policy` directory

You'll be prompted to enter the path to the best parameters file. The default is `best_params_improved.txt`.

## Step 2: Run the Policy in Production

Once the policy is saved, you can run it in production mode.

```bash
python run_production_policy.py
```

This script will:
- Load the saved policy
- Connect to OMNeT++ to receive real data
- Use the policy to make decisions without any training or exploration
- Collect performance statistics
- Save statistics to the `production_stats` directory

You'll be prompted to enter the path to the saved policy. The default is `saved_policies/production_policy`.

### Production Mode Features

- **Real Data Only**: The policy will only use real data from OMNeT++
- **Pause When No Data**: The policy will pause when no data is available
- **Performance Monitoring**: The script tracks metrics like reward, steps per second, etc.
- **Statistics Saving**: Performance statistics are saved for later analysis

## Step 3: Analyze Performance

After running the policy in production, you can analyze its performance.

```bash
python analyze_production_performance.py
```

This script will:
- Load all statistics files from the `production_stats` directory
- Generate a summary of performance metrics
- Create visualizations of performance over time
- Save the summary and visualizations to the `production_stats` directory

## File Structure

```
dynamicRL01/
├── run_best_optuna.py           # Training script (existing)
├── save_best_policy.py          # Script to save policy for production
├── run_production_policy.py     # Script to run policy in production
├── analyze_production_performance.py  # Script to analyze performance
├── saved_policies/              # Directory for saved policies
│   └── production_policy/       # Default location for production policy
├── production_stats/            # Directory for production statistics
│   ├── run_stats_*.json         # Statistics from each production run
│   ├── performance_summary.txt  # Summary of all production runs
│   └── *.png                    # Performance visualizations
└── README_PRODUCTION.md         # This file
```

## Troubleshooting

### Connection Issues

If you encounter connection issues with OMNeT++:

1. Make sure OMNeT++ is running and ready to accept connections
2. Check that port 5555 is not being used by another process
3. Try restarting both OMNeT++ and the Python script

### Policy Loading Issues

If you have trouble loading the saved policy:

1. Make sure you've run `save_best_policy.py` first
2. Check that the policy directory exists and contains the saved model files
3. Ensure you're using the same version of TensorFlow for saving and loading

### Performance Issues

If the policy is not performing well in production:

1. Check that OMNeT++ is sending the expected data
2. Verify that the policy was trained with similar data
3. Consider retraining the policy with more diverse data

## Advanced Usage

### Custom Batch Size

By default, the production script uses a batch size of 1. If you need to process multiple inputs at once, you can modify the `batch_size` parameter in `run_production_policy.py`.

### Multiple Policies

You can save multiple policies with different parameters by changing the output directory in `save_best_policy.py`. Then you can compare their performance in production.

### Continuous Monitoring

For long-running production deployments, consider setting up a monitoring system that periodically runs `analyze_production_performance.py` to track performance over time.

## Contact

If you encounter any issues or have questions, please contact the development team.
