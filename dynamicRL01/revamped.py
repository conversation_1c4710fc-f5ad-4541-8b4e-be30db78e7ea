import logging  # Add logging module
import zmq
import json
import time
import threading
import numpy as np
from collections import defaultdict
import re
from threading import Lock
import os
import tensorflow as tf
from tf_agents.agents.ppo import ppo_agent
from tf_agents.environments import py_environment
from tf_agents.specs import array_spec
from tf_agents.trajectories import time_step as ts
from tf_agents.environments import tf_py_environment
from tf_agents.networks import actor_distribution_network, value_network
from tf_agents.networks import nest_map
from tf_agents.networks import encoding_network
from tf_agents.networks import network
from tf_agents.trajectories import trajectory
from tf_agents.replay_buffers import tf_uniform_replay_buffer
from tf_agents.utils import common
from tf_agents.drivers import driver, dynamic_episode_driver, dynamic_step_driver
from tf_agents.utils import nest_utils
import optuna

# Configure logging
logging.basicConfig(level=logging.DEBUG, filename='app.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a')

class TSNGCLEnvironment(py_environment.PyEnvironment):
    def __init__(self, port=5555):
        super(TSNGCLEnvironment, self).__init__()
        logging.info("Initializing TSN GCL Environment...")
        print("Initializing TSN GCL Environment...")

        # Initialize ZeroMQ context and socket with more robust settings
        try:
            # First try to clean up any existing ZMQ context
            try:
                zmq.Context.instance().term()
                time.sleep(1)
                zmq._context_initialized = False
            except:
                pass

            # Create a fresh context
            self.context = zmq.Context()
            self.socket = self.context.socket(zmq.ROUTER)

            # Set socket options before binding
            self.socket.setsockopt(zmq.SNDHWM, 10000)
            self.socket.setsockopt(zmq.RCVHWM, 10000)
            self.socket.setsockopt(zmq.RCVTIMEO, 500)  # 500ms timeout for receive operations
            self.socket.setsockopt(zmq.LINGER, 0)      # Don't wait on close

            # Bind to all interfaces
            self.socket.bind(f"tcp://*:{port}")
            logging.info(f"ZeroMQ socket bound to port {port}")
        except Exception as e:
            logging.error(f"Error initializing ZMQ socket: {e}")
            print(f"Error initializing ZMQ socket: {e}")
            raise

        # Define expected queues (3 switches, 3 queues each)
        self.expected_queues = [
            "S0.eth[2].macLayer.queue.queue[0]",  # Network control
            "S0.eth[2].macLayer.queue.queue[1]",  # Video
            "S0.eth[2].macLayer.queue.queue[2]",  # Best effort
            "S1.eth[1].macLayer.queue.queue[0]",  # Network control
            "S1.eth[1].macLayer.queue.queue[1]",  # Video
            "S1.eth[1].macLayer.queue.queue[2]",  # Best effort
            "S2.eth[1].macLayer.queue.queue[0]",  # Network control
            "S2.eth[1].macLayer.queue.queue[1]",  # Video
            "S2.eth[1].macLayer.queue.queue[2]",  # Best effort
        ]
        logging.info(f"Expected queues configured: {len(self.expected_queues)} queues")

        self.num_queues = len(self.expected_queues)
        self.valid_queue_pattern = re.compile(
            r"S0\.eth\[2\]\.macLayer\.queue\.queue\[[0-2]\]$|S[1-2]\.eth\[1\]\.macLayer\.queue\.queue\[[0-2]\]$"
        )

        # State management
        self.queue_states = {queue_id: {} for queue_id in self.expected_queues}
        self.state_batch = defaultdict(list)
        self.start_time = defaultdict(lambda: None)
        self.last_batch_process_time = {}  # Dictionary to track last batch processing time for each queue
        self.lock = Lock()
        self.running = True
        self.current_episode_step = 0
        self.max_episode_steps = 1000

        # GCL configuration (initial values)
        self.gcl_config = np.array([4.0, 3.0, 2.0, 1.0])  # [NC, Video, BE, All closed]
        logging.info(f"Initial GCL configuration: {self.gcl_config}")

        # Define action and observation specs, changed minimum 1 to 0
        self._action_spec = array_spec.BoundedArraySpec(
            shape=(4,), dtype=np.float32, minimum=0.0, maximum=5.0, name='action'
        )

        # Using a flat observation space instead of a dictionary
        # This simplifies the network architecture
        self._observation_spec = array_spec.BoundedArraySpec(
            shape=(self.num_queues * 3 + 4,),  # {queue_lengths + occupancy + rates}/queue + gcl
            dtype=np.float32,
            minimum=0.0,
            maximum=1000.0,
            name='observation'
        )
        self.identiy_map = {}
        logging.info("Action and observation specs defined")

        # Add data activity tracking - ALWAYS start with data_active=True
        self.last_data_received_time = time.time()
        self.data_active = True  # Always start in active state
        self.data_check_interval = 1.0  # Check every 1 second for more responsive pausing

        logging.info("Data activity tracking initialized with active=True")

        # Start background thread for collecting data
        self.state_thread = threading.Thread(target=self._state_monitor)
        self.state_thread.daemon = True
        self.state_thread.start()
        logging.info("State monitor thread started")

        # Start data activity monitor thread
        self.activity_thread = threading.Thread(target=self._monitor_data_activity)
        self.activity_thread.daemon = True
        self.activity_thread.start()
        logging.info("Data activity monitor thread started")

        # Print ready message
        print("TSN GCL Environment initialized and ready to accept data!")
        logging.info("TSN GCL Environment initialized and ready to accept data!")


    def _monitor_data_activity(self):
        """Monitor if data is being received from simulation."""
        logging.info("Data activity monitor started")

        # Log initial status
        logging.info("Data stream is ACTIVE - agent will accept all data")

        while self.running:
            # Always force data_active to True to ensure the agent always accepts data
            self.data_active = True

            # Update last_data_received_time periodically to prevent timeouts
            self.last_data_received_time = time.time()

            # Log status update every minute
            if int(time.time()) % 60 == 0:  # Every minute
                logging.info("Data stream remains ACTIVE - agent is accepting all data")

            time.sleep(self.data_check_interval)

    def action_spec(self):
        return self._action_spec

    def observation_spec(self):
        return self._observation_spec


    def _reset(self):
        """Reset the environment state."""
        logging.info("Resetting environment state...")
        self.current_episode_step = 0

        # Initialize with default values: [NC, Video, BE, guard band]
        # 4ms for NC, 3ms for Video, 2ms for BE, 1ms guard band (total 10ms cycle)
        self.gcl_config = np.array([4.0, 3.0, 2.0, 1.0])

        observation = self._get_flattened_observation()
        logging.debug(f"Environment reset. Initial observation: {observation}")
        return ts.restart(observation)

    def _get_flattened_observation(self):
        """Convert the structured observation to a flat array."""
        logging.debug("Generating flattened observation...")
        queue_lengths = np.zeros(self.num_queues, dtype=np.float32)
        queue_occupancy = np.zeros(self.num_queues, dtype=np.float32)
        arrival_rates = np.zeros(self.num_queues, dtype=np.float32)

        # Track if we have any valid data
        has_valid_data = False

        with self.lock:
            for i, queue_id in enumerate(self.expected_queues):
                if queue_id in self.queue_states and self.queue_states[queue_id]:
                    # Extract values with default fallbacks
                    queue_lengths[i] = self.queue_states[queue_id].get('num_packets', 0.0)
                    queue_occupancy[i] = self.queue_states[queue_id].get('queue_occupancy', 0.0)
                    arrival_rates[i] = self.queue_states[queue_id].get('packetArrivalRate', 0.0)
                    has_valid_data = True
                logging.debug(f"Queue {queue_id} state: {self.queue_states[queue_id] if queue_id in self.queue_states else 'No data'}")

        # If we don't have any valid data, use default values that won't disrupt training
        if not has_valid_data:
            logging.warning("No valid queue data available, using default values")
            # Use small non-zero values to avoid numerical issues
            queue_lengths = np.ones(self.num_queues, dtype=np.float32) * 0.1
            queue_occupancy = np.ones(self.num_queues, dtype=np.float32) * 0.1
            arrival_rates = np.ones(self.num_queues, dtype=np.float32) * 0.1

        # Use current GCL config
        scaled_gcl = self.gcl_config

        # Ensure all arrays have the right shape before concatenation
        if len(queue_lengths) != self.num_queues:
            queue_lengths = np.zeros(self.num_queues, dtype=np.float32)
        if len(queue_occupancy) != self.num_queues:
            queue_occupancy = np.zeros(self.num_queues, dtype=np.float32)
        if len(arrival_rates) != self.num_queues:
            arrival_rates = np.zeros(self.num_queues, dtype=np.float32)

        # Ensure GCL config has the right shape (4,)
        if len(scaled_gcl) != 4:
            scaled_gcl = np.array([4.0, 3.0, 2.0, 1.0], dtype=np.float32)

        observation = np.concatenate([queue_lengths, queue_occupancy, arrival_rates, scaled_gcl]).astype(np.float32)
        logging.debug(f"Flattened observation: {observation}")
        return observation

    def _step(self, action):
        """Execute one time step within the environment."""
        # Add debug loggers to the _Step function:
        logging.info(f"Executing step {self.current_episode_step + 1} with action: {action}")

        # Even if data_active is False, we'll continue processing
        # This ensures the agent always accepts data from OMNeT++
        if not self.data_active:
            logging.info("No recent data from omnet++, but continuing anyway.")
            # Force data_active to True to ensure we keep processing
            self.data_active = True
            # Update last_data_received_time to prevent waiting
            self.last_data_received_time = time.time()

        if self.current_episode_step >= self.max_episode_steps:
            logging.info("Max episode steps reached. Resetting environment.")
            return self.reset()

        self.current_episode_step += 1

        # First clip actions to valid range [0, 10]
        clipped_action = np.clip(action, 0.0, 10.0)
        logging.debug(f"Clipped action: {clipped_action}")

        # Get the first 3 elements (gate durations)
        gate_durations = clipped_action[:3].copy()

        # Ensure no negative values
        gate_durations = np.maximum(gate_durations, 0.0)

        # Normalize gate durations to sum to 9ms (fixed cycle time of 10ms - 1ms guard band)
        total_duration = np.sum(gate_durations)
        if total_duration > 0:
            gate_durations = gate_durations * (9.0 / total_duration)
        else:
            # If all durations are 0, distribute evenly
            gate_durations = np.ones(3) * 3.0  # 3ms each for 9ms total

        # Create final GCL config: [NC duration, Video duration, BE duration, guard band]
        self.gcl_config = np.zeros(4, dtype=np.float32)
        self.gcl_config[:3] = gate_durations
        self.gcl_config[3] = 1.0  # Fixed 1ms guard band

        # Double-check all values are within bounds
        self.gcl_config = np.clip(self.gcl_config, 0.0, 10.0)

        # Ensure total cycle time is exactly 10ms by adjusting the gate durations if needed
        total_cycle_time = np.sum(self.gcl_config)
        if abs(total_cycle_time - 10.0) > 1e-6:  # Use small epsilon for floating point comparison
            # Calculate the difference
            diff = 10.0 - total_cycle_time

            # First try to adjust the BE duration (index 2) to make the total exactly 10ms
            self.gcl_config[2] += diff

            # Ensure it's still within bounds
            self.gcl_config[2] = np.clip(self.gcl_config[2], 0.0, 9.0)

            # Recalculate total to verify
            total_cycle_time = np.sum(self.gcl_config)

            # If we still don't have exactly 10ms, adjust the other durations
            if abs(total_cycle_time - 10.0) > 1e-6:
                diff = 10.0 - total_cycle_time

                # Try to distribute the difference proportionally among all gate durations
                # while keeping the guard band fixed
                total_gates = np.sum(self.gcl_config[:3])
                if total_gates > 0:
                    # Calculate adjustment factors
                    factors = self.gcl_config[:3] / total_gates
                    # Apply adjustments
                    self.gcl_config[:3] += factors * diff
                    # Ensure all values are within bounds
                    self.gcl_config[:3] = np.clip(self.gcl_config[:3], 0.0, 9.0)
                else:
                    # If all gates are 0, distribute evenly
                    self.gcl_config[:3] = np.array([3.0, 3.0, 3.0])

                # Final check - force exact 10ms by adjusting the last value if needed
                total_cycle_time = np.sum(self.gcl_config)
                if abs(total_cycle_time - 10.0) > 1e-6:
                    # As a last resort, force the total to be exactly 10ms
                    self.gcl_config[3] = 10.0 - np.sum(self.gcl_config[:3])
                    self.gcl_config[3] = max(0.1, self.gcl_config[3])  # Ensure guard band is at least 0.1ms

                    # If guard band adjustment would make total != 10ms, adjust BE again
                    total_cycle_time = np.sum(self.gcl_config)
                    if abs(total_cycle_time - 10.0) > 1e-6:
                        self.gcl_config[2] = 10.0 - (self.gcl_config[0] + self.gcl_config[1] + self.gcl_config[3])
                        self.gcl_config[2] = max(0.1, self.gcl_config[2])  # Ensure BE is at least 0.1ms

            # Final verification
            total_cycle_time = np.sum(self.gcl_config)

        logging.info(f"Normalized GCL config: {self.gcl_config}, Total cycle time: {total_cycle_time}ms")
        logging.info(f"Gate durations: NC={self.gcl_config[0]:.2f}ms, Video={self.gcl_config[1]:.2f}ms, BE={self.gcl_config[2]:.2f}ms, Guard={self.gcl_config[3]:.2f}ms")

        # Process any pending batch data before sending actions
        logging.debug("_step method called - checking for pending batch data")

        with self.lock:
            pending_batches = 0
            for queue_id in self.expected_queues:
                if queue_id in self.state_batch and self.state_batch[queue_id]:
                    # Only process if we have data
                    if len(self.state_batch[queue_id]) > 0:
                        pending_batches += 1
                        logging.debug(f"Found pending batch data for queue {queue_id} with {len(self.state_batch[queue_id])} entries")

                        # Make a copy of the batch data before processing
                        batch_to_process = self.state_batch[queue_id].copy()

                        # Clear the batch before processing to avoid potential race conditions
                        self.state_batch[queue_id] = []

                        # Process the batch data
                        self._handle_batch_data(queue_id, batch_to_process)
                        logging.debug(f"Finished processing pending batch data for queue {queue_id}")

            if pending_batches > 0:
                logging.info(f"Processed {pending_batches} pending batches in _step method")

        # Send actions after processing latest state
        for queue_id in self.expected_queues:
            self._send_action_to_omnet(queue_id, self.gcl_config)

        # Reduced sleep time to be more responsive
        time.sleep(0.005)
        observation = self._get_flattened_observation()
        logging.debug(f"Observation: {observation}")
        reward = self._calculate_reward(observation)
        logging.debug(f"Step reward: {reward}")

        if self.current_episode_step >= self.max_episode_steps:
            logging.info("Episode terminated.")
            return ts.termination(observation, reward)
        else:
            return ts.transition(observation, reward, discount=0.99)

    def _calculate_reward(self, observation):
        """Calculate reward based on flattened observation."""
        logging.debug("Calculating reward...")
        n = self.num_queues
        logging.debug(f"Observation used for reward computation: {observation}")
        queue_lengths = observation[:n]
        queue_occupancy = observation[n:2*n]
        arrival_rates = observation[2*n:3*n]
        gcl_config = observation[3*n:]

        logging.debug(f"Queue lengths: {queue_lengths}, Queue occupancy: {queue_occupancy}, Arrival rates: {arrival_rates}, GCL: {gcl_config}")

        # Modify reward calculation to provide better learning signals

        # Penalize high queue occupancy (lower is better)
        # Reduce the penalty weight to avoid dominating the reward
        occupancy_penalty = -np.mean(queue_occupancy) * 0.5  # Reduced from 1.0

        # Reward higher arrival rates (higher is better)
        # Increase the weight to make this component more significant
        handling_reward = np.mean(arrival_rates) / 50.0  # Increased from 100.0

        # Prioritize different queue types
        queue_type_weights = np.array([3.0, 2.0, 1.0] * 3)  # Higher priority for network control, then video

        # Reward lower queue lengths (lower is better)
        queue_length_penalty = -np.sum(queue_lengths * queue_type_weights) / 200.0  # Reduced from 100.0

        # Penalize queue imbalance (more balanced is better)
        # Use standard deviation instead of absolute difference for better measure of imbalance
        queue_imbalance = np.std(queue_lengths)
        imbalance_penalty = -queue_imbalance / 20.0  # Reduced from 10.0

        # Add a small reward for exploration to avoid getting stuck
        exploration_bonus = np.random.normal(0, 0.05)  # Reduced from 0.1

        # Add a base reward to offset negative values
        base_reward = 1.0  # New component to ensure rewards aren't always negative

        # Calculate total reward with adjusted weights
        total_reward = (
            base_reward +                  # Base reward to offset negative values
            occupancy_penalty * 0.3 +      # 30% weight
            handling_reward * 0.3 +        # 30% weight
            queue_length_penalty * 0.2 +   # 20% weight
            imbalance_penalty * 0.2 +      # 20% weight
            exploration_bonus              # Small random noise
        )

        # Clip reward to avoid extreme values that might destabilize learning
        total_reward = np.clip(total_reward, -10.0, 10.0)  # Narrower range from -20.0, 20.0

        logging.debug(f"Reward components - Base: {base_reward}, Occupancy penalty: {occupancy_penalty}, "
                     f"Handling reward: {handling_reward}, Queue length penalty: {queue_length_penalty}, "
                     f"Imbalance penalty: {imbalance_penalty}, Exploration: {exploration_bonus}, "
                     f"Total reward: {total_reward}")

        return float(total_reward)

    def _state_monitor(self):
        """Background thread to monitor and collect state from OMNeT++."""
        logging.info("Starting state monitor...")
        poller = zmq.Poller()
        poller.register(self.socket, zmq.POLLIN)

        # Set socket options for more reliability
        self.socket.setsockopt(zmq.RCVTIMEO, 500)  # Increased timeout from 100ms to 500ms
        self.socket.setsockopt(zmq.LINGER, 0)      # Don't wait on close

        consecutive_errors = 0
        max_consecutive_errors = 5

        # Force data_active to True at the start
        self.data_active = True
        self.last_data_received_time = time.time()
        logging.info("Forcing data stream to active state at monitor thread start.")

        # Add debug message to show socket is ready
        logging.info(f"ZMQ socket bound and ready to receive data on port 5555")
        print(f"ZMQ socket bound and ready to receive data on port 5555")

        while self.running:
            try:
                # Always set data_active to True to ensure the agent keeps running
                self.data_active = True

                # Update last_data_received_time periodically even without data
                # This prevents the agent from thinking there's no data
                self.last_data_received_time = time.time()

                # Use a longer timeout for more reliable detection
                events = dict(poller.poll(timeout=100))  # Increased from 20ms to 100ms

                # Print periodic status updates
                if int(time.time()) % 10 == 0:  # Every 10 seconds
                    print(f"State monitor active, waiting for data...")
                    logging.info("State monitor active, waiting for data...")

                if self.socket in events and events[self.socket] == zmq.POLLIN:
                    # Reset consecutive errors counter on successful poll
                    consecutive_errors = 0

                    try:
                        identity, message = self.socket.recv_multipart(zmq.NOBLOCK)
                        # Update the last data received time
                        self.last_data_received_time = time.time()

                        # Log successful data reception
                        print(f"Received data from OMNeT++")
                        logging.info("Received data from OMNeT++")

                        # Check if the received message is empty
                        if not message:
                            logging.warning("Received an empty message from omnet++.")
                            continue

                        data = json.loads(message.decode())

                        if 'queue_id' in data:
                            queue_id_raw = data['queue_id']
                            if "rlTest01." in queue_id_raw:
                                queue_id = queue_id_raw.split("rlTest01.")[-1]
                            else:
                                queue_id = queue_id_raw
                            logging.info(f"Received message from omnet++ with queue_id: {queue_id}")
                            print(f"Received message from omnet++ with queue_id: {queue_id}")

                            # Store identity for this queue
                            self.identiy_map[queue_id] = identity

                            # Be more lenient with queue validation
                            # Even if queue ID doesn't match expected pattern, still process it
                            if queue_id not in self.expected_queues:
                                logging.warning(f"Unexpected queue ID received: {queue_id}, but processing anyway")
                                print(f"Unexpected queue ID received: {queue_id}, but processing anyway")
                                # Add it to expected queues to handle it in the future
                                self.expected_queues.append(queue_id)
                                self.num_queues = len(self.expected_queues)
                                self.queue_states[queue_id] = {}

                        if 'state' in data and len(data['state']) >= 5:
                            logging.info(f"Processing state data for queue: {queue_id}")
                            print(f"Processing state data for queue: {queue_id}")
                            current_time_ms = round(float(data['state'][4]) * 1000, 2)

                            # Create state entry for batch processing
                            current_state = {
                                "num_packets": float(data['state'][0]),
                                "queue_occupancy": float(data['state'][1]),
                                "packetArrivalRate": float(data['state'][2]),
                                "deadline": float(data['state'][3]),
                                "current_sim_time": current_time_ms
                            }

                            # Add to batch for this queue
                            self.state_batch[queue_id].append(current_state)
                            logging.debug(f"Added data to batch for queue {queue_id}, current size: {len(self.state_batch[queue_id])}")

                            # Get current time in microseconds
                            current_time_us = int(time.time() * 1000000)

                            # Store the current simulation time for this queue if not already set
                            if queue_id not in self.last_batch_process_time:
                                self.last_batch_process_time[queue_id] = current_time_us
                                logging.debug(f"Initialized last_batch_process_time for queue {queue_id}: {current_time_us}")

                            # Check if 200 microseconds have passed since the last batch processing for this queue
                            time_since_last_process = current_time_us - self.last_batch_process_time[queue_id]

                            # Process batch data if 200 microseconds have passed or we have enough entries
                            if time_since_last_process >= 200 or len(self.state_batch[queue_id]) >= 2:
                                if time_since_last_process >= 200:
                                    logging.info(f"Time threshold reached for queue {queue_id}: {time_since_last_process} microseconds")
                                else:
                                    logging.info(f"Batch size threshold reached for queue {queue_id} with {len(self.state_batch[queue_id])} entries")

                                # Update the last batch process time
                                self.last_batch_process_time[queue_id] = current_time_us

                                # Make a copy of the batch data before processing
                                batch_to_process = self.state_batch[queue_id].copy()

                                # Clear the batch before processing to avoid potential race conditions
                                self.state_batch[queue_id] = []

                                # Process the batch data
                                self._handle_batch_data(queue_id, batch_to_process)
                            else:
                                # Only log the data collection, don't update immediate state
                                logging.debug(f"Collected data point {len(self.state_batch[queue_id])}/2 for queue {queue_id}")

                            # Log received state data at debug level
                            logging.debug(f"Received raw state data for queue {queue_id}")
                    except zmq.ZMQError as zmq_err:
                        if zmq_err.errno == zmq.EAGAIN:
                            # Non-blocking mode returned no messages, just continue
                            pass
                        else:
                            logging.warning(f"ZMQ error in receive: {zmq_err}")
                            print(f"ZMQ error in receive: {zmq_err}")
                            consecutive_errors += 1
            except Exception as e:
                logging.error(f"Error in state monitor: {e}")
                print(f"Error in state monitor: {e}")
                consecutive_errors += 1

                # If we get too many consecutive errors, try to reset the socket
                if consecutive_errors >= max_consecutive_errors:
                    logging.warning(f"Too many consecutive errors ({consecutive_errors}). Attempting to reset ZMQ socket.")
                    print(f"Too many consecutive errors ({consecutive_errors}). Attempting to reset ZMQ socket.")
                    try:
                        # Close and recreate the socket
                        poller.unregister(self.socket)
                        self.socket.close()
                        self.socket = self.context.socket(zmq.ROUTER)
                        self.socket.bind("tcp://*:5555")
                        self.socket.setsockopt(zmq.SNDHWM, 10000)
                        self.socket.setsockopt(zmq.RCVHWM, 10000)
                        self.socket.setsockopt(zmq.RCVTIMEO, 500)  # Increased timeout
                        self.socket.setsockopt(zmq.LINGER, 0)
                        poller.register(self.socket, zmq.POLLIN)
                        logging.info("ZMQ socket reset successfully.")
                        print("ZMQ socket reset successfully.")
                        consecutive_errors = 0
                    except Exception as reset_err:
                        logging.error(f"Failed to reset ZMQ socket: {reset_err}")
                        print(f"Failed to reset ZMQ socket: {reset_err}")

            # Short sleep to prevent CPU overuse
            time.sleep(0.01)  # Increased from 0.001 to 0.01 to reduce CPU usage

    def _handle_batch_data(self, queue_id, batch_data):
        """Process batch data for a queue."""
        # Log batch processing with appropriate level
        logging.debug(f"Processing batch data for queue {queue_id} with {len(batch_data) if batch_data else 0} entries")

        if not batch_data:
            logging.warning(f"Empty batch data for queue {queue_id}, skipping processing")
            return

        # Handle the case where there's only one entry in the batch
        # This might happen with the 200 microsecond time threshold
        if len(batch_data) == 1:
            logging.debug(f"Single entry batch for queue {queue_id}, using direct values")

            # Use the values directly from the single entry
            avg_num_packets = batch_data[0].get("num_packets", 0)
            avg_queue_occupancy = batch_data[0].get("queue_occupancy", 0)
            avg_packet_arrival_rate = batch_data[0].get("packetArrivalRate", 0)
        else:
            # Calculate averages from batch data for multiple entries
            avg_num_packets = np.mean([d.get("num_packets", 0) for d in batch_data])
            avg_queue_occupancy = np.mean([d.get("queue_occupancy", 0) for d in batch_data])
            avg_packet_arrival_rate = np.mean([d.get("packetArrivalRate", 0) for d in batch_data])

        # Log batch processing results at info level
        logging.info(f"Batch processing for {queue_id}: packets={avg_num_packets:.2f}, occupancy={avg_queue_occupancy:.2f}, rate={avg_packet_arrival_rate:.2f}")

        # Update queue states with batch-processed data
        with self.lock:
            if queue_id in self.queue_states:
                self.queue_states[queue_id].update({
                    "num_packets": avg_num_packets,
                    "queue_occupancy": avg_queue_occupancy,
                    "packetArrivalRate": avg_packet_arrival_rate
                })
                logging.debug(f"Updated queue {queue_id} state with batch-processed data")
            else:
                logging.error(f"Queue {queue_id} not found in queue_states")

        # Signal that we've processed a batch of data
        logging.debug(f"Successfully processed batch data for queue {queue_id}")

    def _send_action_to_omnet(self, queue_id, action):
        """Send action to OMNeT++ simulation."""
        try:
            with self.lock:
                # before seding, check if the queue_id is in the identity map
                if queue_id not in self.identiy_map:
                    logging.warning(f"Queue ID not found in identity map: {queue_id}")
                    return
                client_id = self.identiy_map[queue_id]
                logging.debug(f"Sending action to OMNeT++ for queue {queue_id} with identity {client_id}: {action}")
                self.socket.send_multipart([
                    client_id,
                    b'',
                    json.dumps({"action": action.tolist()}).encode()
                ])
            # logging.debug(f"Action sent to OMNeT++ for queue {queue_id}: {action}")
        except Exception as e:
            logging.error(f"Error sending action to OMNeT++: {e}")

    def close(self):
        """Close the environment."""
        self.running = False
        if self.state_thread.is_alive():
            self.state_thread.join(timeout=1.0)
        if self.activity_thread.is_alive():
            self.activity_thread.join(timeout=1.0)
        self.socket.close()
        self.context.term()
        super(TSNGCLEnvironment, self).close()

def create_ppo_agent(env,
                     learning_rate=5e-5,  # Reduced from 1e-4
                     optimizer_class=tf.keras.optimizers.Adam,
                     fc_layer_params=(64, 64),  # Simpler network
                     activation_fn=tf.keras.activations.relu,
                     entropy_regularization=0.01,  # Reduced from 0.05
                     importance_ratio_clipping=0.2,
                     num_epochs=3):  # Reduced from 5
    """Create a PPO agent for the TSN GCL environment."""

    tf_env = tf_py_environment.TFPyEnvironment(env)

    # Create a simpler actor network
    actor_net = actor_distribution_network.ActorDistributionNetwork(
        tf_env.observation_spec(),
        tf_env.action_spec(),
        fc_layer_params=fc_layer_params,
        activation_fn=activation_fn,
        kernel_initializer=tf.keras.initializers.VarianceScaling(
            scale=1.0, mode='fan_in', distribution='truncated_normal'
        )
    )

    # Create a simpler value network
    value_net = value_network.ValueNetwork(
        tf_env.observation_spec(),
        fc_layer_params=fc_layer_params,
        activation_fn=activation_fn,
        kernel_initializer=tf.keras.initializers.VarianceScaling(
            scale=1.0, mode='fan_in', distribution='truncated_normal'
        )
    )

    # Use a learning rate schedule that decays over time
    lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
        initial_learning_rate=learning_rate,
        decay_steps=1000,
        decay_rate=0.95,
        staircase=True
    )

    optimizer = optimizer_class(learning_rate=lr_schedule)

    agent = ppo_agent.PPOAgent(
        tf_env.time_step_spec(),
        tf_env.action_spec(),
        actor_net=actor_net,
        value_net=value_net,
        optimizer=optimizer,
        entropy_regularization=entropy_regularization,
        importance_ratio_clipping=importance_ratio_clipping,
        normalize_observations=True,
        normalize_rewards=True,
        use_gae=True,
        lambda_value=0.95,  # GAE parameter
        discount_factor=0.99,  # Increased from default
        num_epochs=num_epochs,
        debug_summaries=True,  # Enable debug summaries
        summarize_grads_and_vars=True,  # Summarize gradients and variables
        train_step_counter=tf.Variable(0),
        compute_value_and_advantage_in_train=True  # More accurate value estimation
    )

    agent.initialize()
    return agent, tf_env

def optuna_objective(trial):
    """Optuna objective function to optimize PPO hyperparameters."""

    # Sample hyperparameters with wider ranges and more options
    learning_rate = trial.suggest_loguniform('learning_rate', 1e-6, 1e-2)  # Wider range

    # More network architecture options
    fc_layer_size = trial.suggest_categorical('fc_layer_params', [
        (64, 64),
        (128, 128),
        (256, 256),
        (128, 64),
        (256, 128),
        (512, 256)
    ])

    # More activation function options
    activation_name = trial.suggest_categorical('activation_fn', [
        'relu',
        'elu',
        'tanh',
    ])

    # Wider range for entropy regularization
    entropy_regularization = trial.suggest_uniform('entropy_regularization', 0.01, 0.2)

    # Wider range for importance ratio clipping
    importance_ratio_clipping = trial.suggest_uniform('importance_ratio_clipping', 0.1, 0.5)

    # More epochs options
    num_epochs = trial.suggest_int('num_epochs', 3, 15)

    # Add dropout rate as a hyperparameter
    dropout_rate = trial.suggest_uniform('dropout_rate', 0.0, 0.3)

    # Add early stopping patience as a hyperparameter
    patience = trial.suggest_int('patience', 3, 10)

    # Log trial parameters
    print(f"Trial {trial.number}: Starting with parameters:")
    print(f"  learning_rate: {learning_rate}")
    print(f"  fc_layer_size: {fc_layer_size}")
    print(f"  activation_fn: {activation_name}")
    print(f"  entropy_regularization: {entropy_regularization}")
    print(f"  importance_ratio_clipping: {importance_ratio_clipping}")
    print(f"  num_epochs: {num_epochs}")
    print(f"  dropout_rate: {dropout_rate}")
    print(f"  patience: {patience}")

    # Map activation function
    activation_fn_map = {
        'relu': tf.keras.activations.relu,
        'elu': tf.keras.activations.elu,
        'selu': tf.keras.activations.selu,
        'tanh': tf.keras.activations.tanh,
        'swish': tf.nn.swish  # Also known as SiLU
    }
    activation_fn = activation_fn_map[activation_name]

    logging.info(f"Starting trial {trial.number} with parameters: {trial.params}")
    print(f"\n=== Starting trial {trial.number} with parameters: {trial.params} ===\n")

    # Force ZMQ context cleanup before creating a new environment
    import gc
    import zmq

    # Completely reset ZMQ context between trials
    try:
        zmq.Context.instance().term()
        time.sleep(2)  # Give time for context to terminate
        zmq._context_initialized = False  # Reset the singleton flag
    except Exception as e:
        logging.error(f"Error resetting ZMQ context: {e}")

    gc.collect()
    time.sleep(3)  # Additional delay to ensure resources are freed

    # Setup environment with a timeout for connection
    max_connection_attempts = 5  # Increased from 3
    env = None

    for attempt in range(max_connection_attempts):
        try:
            logging.info(f"Connection attempt {attempt+1}/{max_connection_attempts}")
            print(f"Connection attempt {attempt+1}/{max_connection_attempts}")

            # Create a new environment with each attempt
            env = TSNGCLEnvironment(port=5555)

            # Force data_active to True to ensure the agent always accepts data
            env.data_active = True
            env.last_data_received_time = time.time()

            print(f"Connection attempt {attempt+1} successful - forcing data stream to active state")
            logging.info(f"Connection attempt {attempt+1} successful - forcing data stream to active state")

            # Short wait to allow ZMQ socket to initialize fully
            time.sleep(1)

            # Always consider the connection successful
            logging.info("Successfully connected to OMNeT++ with forced active data stream")
            print("Successfully connected to OMNeT++ with forced active data stream")
            break
        except Exception as e:
            logging.error(f"Error during connection attempt {attempt+1}: {e}")
            print(f"Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None

            # Reset ZMQ context again
            try:
                zmq.Context.instance().term()
                time.sleep(2)
                zmq._context_initialized = False
            except Exception as e:
                logging.error(f"Error resetting ZMQ context: {e}")

            gc.collect()
            time.sleep(5)  # Increased wait time

    if env is None:
        logging.error(f"Failed to create environment for trial {trial.number}")
        print(f"Failed to create environment for trial {trial.number}")
        return 0.0  # Return worst possible score for this trial

    # Force data_active to True to ensure the agent always accepts data
    env.data_active = True
    env.last_data_received_time = time.time()
    logging.info(f"Forcing data stream to active state for trial {trial.number}")
    print(f"Forcing data stream to active state for trial {trial.number}")

    try:
        # Create a custom version of create_ppo_agent that uses dropout_rate
        def create_custom_ppo_agent(env, dropout_rate=0.1):
            tf_env = tf_py_environment.TFPyEnvironment(env)

            # Create actor network with custom dropout
            actor_net = actor_distribution_network.ActorDistributionNetwork(
                tf_env.observation_spec(),
                tf_env.action_spec(),
                fc_layer_params=fc_layer_size,
                activation_fn=activation_fn,
                dropout_layer_params=[dropout_rate] * len(fc_layer_size),  # Apply dropout to each layer
                kernel_initializer=tf.keras.initializers.VarianceScaling(
                    scale=2.0, mode='fan_in', distribution='truncated_normal'
                )
            )

            # Create value network with custom dropout
            value_net = value_network.ValueNetwork(
                tf_env.observation_spec(),
                fc_layer_params=fc_layer_size,
                activation_fn=activation_fn,
                kernel_initializer=tf.keras.initializers.VarianceScaling(
                    scale=2.0, mode='fan_in', distribution='truncated_normal'
                )
            )

            # Use a learning rate schedule that decays over time
            lr_schedule = tf.keras.optimizers.schedules.ExponentialDecay(
                initial_learning_rate=learning_rate,
                decay_steps=1000,
                decay_rate=0.95,
                staircase=True
            )

            optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule)

            agent = ppo_agent.PPOAgent(
                tf_env.time_step_spec(),
                tf_env.action_spec(),
                actor_net=actor_net,
                value_net=value_net,
                optimizer=optimizer,
                entropy_regularization=entropy_regularization,
                importance_ratio_clipping=importance_ratio_clipping,
                normalize_observations=True,
                normalize_rewards=True,
                use_gae=True,
                lambda_value=0.95,
                discount_factor=0.99,
                num_epochs=num_epochs,
                debug_summaries=True,
                summarize_grads_and_vars=True,
                train_step_counter=tf.Variable(0),
                compute_value_and_advantage_in_train=True
            )

            agent.initialize()
            return agent, tf_env

        # Create agent with custom dropout rate
        agent, tf_env = create_custom_ppo_agent(env, dropout_rate=dropout_rate)

        # Create trainer with early stopping patience
        trainer = TSNGCLTrainer(env, agent, tf_env, num_iterations=50, collect_steps_per_iteration=150)
        trainer.patience = patience  # Set patience from hyperparameter
        returns = trainer.run_training()

        # Ensure we have a valid return value even if data becomes inactive
        if returns and len(returns) > 0:
            final_return = returns[-1]
            logging.info(f"Trial {trial.number} completed with final return: {final_return}")
            print(f"Trial {trial.number} completed with final return: {final_return}")
        else:
            final_return = 0.0  # fallback if training failed
            logging.warning(f"Trial {trial.number} failed to produce returns, using default value 0.0")
            print(f"Trial {trial.number} failed to produce returns, using default value 0.0")
    finally:
        # Always close the environment before starting the next trial
        if env:
            logging.info(f"Closing environment for trial {trial.number}")
            print(f"Closing environment for trial {trial.number}")
            env.close()

            # Force a longer delay to ensure ZMQ sockets are fully closed
            time.sleep(5)  # Increased from 1

    return final_return

# def create_ppo_agent(env):
#     """Create a PPO agent for the TSN GCL environment."""
#     # Convert to TF environment
#     tf_env = tf_py_environment.TFPyEnvironment(env)

#     # Create the actor network
#     actor_net = actor_distribution_network.ActorDistributionNetwork(
#         tf_env.observation_spec(),
#         tf_env.action_spec(),
#         fc_layer_params=(64, 64),
#         activation_fn=tf.keras.activations.relu
#     )

#     # Create the value network
#     value_net = value_network.ValueNetwork(
#         tf_env.observation_spec(),
#         fc_layer_params=(64, 64),
#         activation_fn=tf.keras.activations.relu
#     )

#     # Create the optimizer
#     optimizer = tf.keras.optimizers.Adam(learning_rate=1e-4)

#     # Create the PPO agent
#     agent = ppo_agent.PPOAgent(
#         tf_env.time_step_spec(),
#         tf_env.action_spec(),
#         actor_net=actor_net,
#         value_net=value_net,
#         optimizer=optimizer,
#         entropy_regularization=0.01,
#         importance_ratio_clipping=0.2,
#         normalize_observations=True,
#         normalize_rewards=True,
#         use_gae=True,
#         num_epochs=3,
#         train_step_counter=tf.Variable(0)
#     )

#     agent.initialize()
#     return agent, tf_env

class MinimalTrainer:
    """Minimal trainer for PPO agent in TSN GCL environment."""

    def __init__(self, env, agent, tf_env, num_iterations=100):
        self.env = env
        self.agent = agent
        self.tf_env = tf_env
        self.num_iterations = num_iterations

        # Create replay buffer
        self.replay_buffer = tf_uniform_replay_buffer.TFUniformReplayBuffer(
            data_spec=agent.collect_data_spec,
            batch_size=tf_env.batch_size,
            max_length=1000
        )

    def train(self):
        """Run minimal training loop."""
        logging.info("Starting minimal training...")

        for i in range(self.num_iterations):
            # Check if data is active
            if not self.env.data_active:
                logging.info("Waiting for data...")
                time.sleep(1)
                continue

            # Collect one episode
            time_step = self.tf_env.reset()
            episode_return = 0

            while not time_step.is_last():
                # Get action from agent's collect policy
                action_step = self.agent.collect_policy.action(time_step)

                # Take step in environment
                next_time_step = self.tf_env.step(action_step.action)

                # Add experience to replay buffer
                traj = trajectory.from_transition(time_step, action_step, next_time_step)
                self.replay_buffer.add_batch(traj)

                # Update for next iteration
                time_step = next_time_step
                episode_return += time_step.reward

            # Sample a batch and train
            experience = self.replay_buffer.gather_all()
            train_loss = self.agent.train(experience)

            # Clear buffer after training
            self.replay_buffer.clear()

            # Log progress
            if i % 10 == 0:
                logging.info(f"Iteration {i}, Return: {episode_return}, Loss: {train_loss.loss}")

        return "Training complete"


class TSNGCLTrainer:
    def __init__(self, env, agent, tf_env, num_iterations=30, collect_steps_per_iteration=150):
        self.env = env
        self.agent = agent
        self.tf_env = tf_env
        self.num_iterations = num_iterations  # Increased from 20 to 30
        self.collect_steps_per_iteration = collect_steps_per_iteration  # Increased from 100 to 150

        # Initialize last_real_loss and last_avg_return
        self.last_real_loss = None
        self.last_avg_return = None

        # Early stopping parameters
        self.best_loss = float('inf')
        self.best_return = float('-inf')
        self.patience = 5  # Number of iterations to wait for improvement
        self.patience_counter = 0
        self.min_delta_loss = 0.01  # Minimum change in loss to be considered an improvement
        self.min_delta_return = 0.1  # Minimum change in return to be considered an improvement

        # Create a replay buffer
        self.replay_buffer = tf_uniform_replay_buffer.TFUniformReplayBuffer(
            data_spec=agent.collect_data_spec,
            batch_size=tf_env.batch_size,
            max_length=10000
        )

        # Create a driver for collecting experience
        self.collect_driver = dynamic_step_driver.DynamicStepDriver(
            self.tf_env,
            self.agent.collect_policy,
            observers=[self.replay_buffer.add_batch],
            num_steps=collect_steps_per_iteration
        )

    def collect_data(self):
        """Collect data using the collect driver."""
        # Always collect data, even if the environment reports data stream as inactive
        # This ensures the agent always processes data from OMNeT++
        if not self.env.data_active:
            logging.info("Data stream appears inactive, but continuing with data collection anyway")
            # Force data_active to True to ensure data collection continues
            self.env.data_active = True
            # Update last_data_received_time to prevent waiting
            self.env.last_data_received_time = time.time()

        try:
            self.collect_driver.run()
            logging.debug(f"Data collection completed, buffer now has {self.replay_buffer.num_frames()} frames")
        except Exception as e:
            logging.error(f"Error during data collection: {e}")
            import traceback
            logging.error(traceback.format_exc())

    def train(self):
        """Train the agent using collected data."""
        # Even if data is not active, we'll continue with training
        # This ensures the agent always processes data from OMNeT++
        if not self.env.data_active:
            logging.info("Data stream appears inactive, but continuing with training anyway")
            # Force data_active to True to ensure training continues
            self.env.data_active = True
            # Update last_data_received_time to prevent waiting
            self.env.last_data_received_time = time.time()

        # Check if replay buffer has enough data
        if self.replay_buffer.num_frames() < 64:  # Minimum batch size
            logging.info("Not enough data in replay buffer for training, collecting more data")

            # Try to collect more data
            try:
                # Force data collection even if data_active is False
                self.env.data_active = True
                self.collect_driver.run()
                logging.info(f"Collected additional data, buffer now has {self.replay_buffer.num_frames()} frames")
            except Exception as e:
                logging.error(f"Error collecting additional data: {e}")

            # If still not enough data, use previous loss or dummy loss
            if self.replay_buffer.num_frames() < 64:
                if self.last_real_loss is not None:
                    return self.last_real_loss
                else:
                    # Create a dummy loss object with the same structure
                    class DummyLoss:
                        def __init__(self):
                            self.loss = 0.1  # Small non-zero value
                    return DummyLoss()

        try:
            # Get dataset from replay buffer
            dataset = self.replay_buffer.as_dataset(
                num_parallel_calls=3,
                sample_batch_size=min(64, self.replay_buffer.num_frames()),  # Use available frames if less than 64
                num_steps=2
            ).prefetch(3)

            # Train the agent
            iterator = iter(dataset)
            experience, _ = next(iterator)
            train_loss = self.agent.train(experience)

            # Store the real loss for future reference
            self.last_real_loss = train_loss

            return train_loss
        except Exception as e:
            logging.error(f"Error during training: {e}")
            import traceback
            logging.error(traceback.format_exc())

            if self.last_real_loss is not None:
                return self.last_real_loss
            else:
                # Create a dummy loss object with the same structure
                class DummyLoss:
                    def __init__(self):
                        self.loss = 0.1  # Small non-zero value
                return DummyLoss()

    def run_training(self):
        """Run the training loop."""
        try:
            returns = []
            losses = []
            iteration = 0

            # Always set data_active to True at the start of training
            self.env.data_active = True
            self.env.last_data_received_time = time.time()

            print("Starting training - agent will always accept data from OMNeT++")
            logging.info("Starting training - agent will always accept data from OMNeT++")

            while iteration < self.num_iterations:
                # Always ensure data_active is True before each iteration
                if not self.env.data_active:
                    self.env.data_active = True
                    self.env.last_data_received_time = time.time()
                    logging.info("Forcing data stream to active state in training loop")

                # Collect data
                self.collect_data()

                # Train the agent
                train_loss = self.train()

                # Ensure train_loss is not None
                if train_loss is None:
                    logging.error("Training returned None instead of a loss object")
                    class DummyLoss:
                        def __init__(self):
                            self.loss = 0.1  # Small non-zero value
                    train_loss = DummyLoss()

                losses.append(float(train_loss.loss))

                # Evaluation
                if iteration % 2 == 0:
                    logging.info(f"Starting evaluation at iteration {iteration}")

                    # Increase number of episodes for more stable evaluation
                    num_eval_episodes = 5  # Increased from 3

                    # Add exploration noise during evaluation to avoid getting stuck
                    # This helps the agent explore more of the state space
                    # PPOAgent doesn't have eval_policy attribute, so we'll use policy instead
                    # We'll add noise to the actions during compute_avg_return instead

                    avg_return = self.compute_avg_return(num_episodes=num_eval_episodes)

                    # Ensure avg_return is not None
                    if avg_return is None:
                        logging.error("Evaluation returned None instead of a numeric value")
                        avg_return = 0.1 if not returns else returns[-1]  # Small non-zero value

                    # Convert to float to ensure it's a numeric value
                    try:
                        avg_return_float = float(avg_return)
                    except (TypeError, ValueError) as e:
                        logging.error(f"Could not convert avg_return to float: {e}")
                        avg_return_float = 0.1  # Small non-zero value

                    returns.append(avg_return_float)
                    print(f'Iteration {iteration}: Average Return = {avg_return_float}, Loss = {float(train_loss.loss)}')

                    # Early stopping check
                    improved = False

                    # Check if loss has improved
                    if float(train_loss.loss) < self.best_loss - self.min_delta_loss:
                        self.best_loss = float(train_loss.loss)
                        improved = True
                        logging.info(f"Loss improved to {self.best_loss}")

                    # Check if return has improved
                    if avg_return_float > self.best_return + self.min_delta_return:
                        self.best_return = avg_return_float
                        improved = True
                        logging.info(f"Return improved to {self.best_return}")

                    if improved:
                        self.patience_counter = 0
                    else:
                        self.patience_counter += 1
                        logging.info(f"No improvement for {self.patience_counter} iterations")

                    # Check if we should stop early
                    if self.patience_counter >= self.patience:
                        logging.info(f"Early stopping triggered after {iteration+1} iterations")
                        print(f"Early stopping triggered. Best loss: {self.best_loss}, Best return: {self.best_return}")
                        break

                    # Log loss history for debugging
                    if len(losses) > 1:
                        logging.info(f"Loss history (last 5): {losses[-5:]}")
                        print(f"Loss history (last 5): {losses[-5:]}")

                iteration += 1

                # Short sleep to prevent CPU overuse
                time.sleep(0.01)

            print("Training completed successfully")
            logging.info("Training completed successfully")
            return returns
        except KeyboardInterrupt:
            print("Training interrupted by user.")
            logging.info("Training interrupted by user.")
        except Exception as e:
            print(f"Error during training: {e}")
            logging.error(f"Error during training: {e}")
            import traceback
            logging.error(traceback.format_exc())
            print(traceback.format_exc())
        finally:
            print("Closing environment")
            logging.info("Closing environment")
            self.env.close()

    def compute_avg_return(self, num_episodes=5):
        """Compute average return for evaluation."""
        # Even if data is not active, we'll continue with evaluation
        # This ensures the agent always processes data from OMNeT++
        if not self.env.data_active:
            logging.info("Data stream appears inactive, but continuing with evaluation anyway")
            # Force data_active to True to ensure evaluation continues
            self.env.data_active = True
            # Update last_data_received_time to prevent waiting
            self.env.last_data_received_time = time.time()

        logging.debug("Starting compute_avg_return...")
        total_return = 0.0
        episode_returns = []

        try:
            for episode in range(num_episodes):
                logging.debug(f"Starting evaluation episode {episode+1}/{num_episodes}")
                time_step = self.tf_env.reset()
                episode_return = 0.0
                step_count = 0

                while not time_step.is_last():
                    # Even if data becomes inactive, continue evaluation
                    if not self.env.data_active:
                        logging.info("Data stream became inactive during evaluation, but continuing")
                        # Force data_active to True to ensure evaluation continues
                        self.env.data_active = True
                        # Update last_data_received_time to prevent waiting
                        self.env.last_data_received_time = time.time()

                    # Get action from policy
                    action_step = self.agent.policy.action(time_step)

                    # Add small exploration noise to the action to avoid getting stuck
                    noisy_action = action_step.action.numpy() + np.random.normal(
                        0, 0.1, size=action_step.action.shape)

                    # Clip to ensure actions stay within bounds
                    action_spec = self.tf_env.action_spec()
                    noisy_action = np.clip(
                        noisy_action,
                        action_spec.minimum,
                        action_spec.maximum
                    )

                    # Use the noisy action
                    next_time_step = self.tf_env.step(noisy_action)

                    # Debug the time_step and reward
                    logging.debug(f"Time step type: {next_time_step.step_type}")
                    logging.debug(f"Raw reward: {next_time_step.reward}")

                    # Safely extract reward value
                    try:
                        if hasattr(next_time_step.reward, 'numpy'):
                            reward_array = next_time_step.reward.numpy()
                            if isinstance(reward_array, np.ndarray) and reward_array.size > 0:
                                reward = float(reward_array[0])
                            else:
                                reward = float(reward_array)
                        else:
                            reward = float(next_time_step.reward)

                        logging.debug(f"Processed reward: {reward}")
                    except Exception as e:
                        logging.warning(f"Error extracting reward: {e}. Using default value 0.0")
                        reward = 0.0

                    episode_return += reward
                    time_step = next_time_step
                    step_count += 1

                    # Limit maximum steps per episode to avoid infinite loops
                    if step_count >= 100:  # Reasonable limit for evaluation
                        logging.warning(f"Reached maximum steps ({step_count}) for evaluation episode, breaking")
                        break

                    logging.debug(f"Step {step_count}, cumulative return: {episode_return}")

                logging.info(f"Evaluation episode {episode+1} completed with return: {episode_return}")
                episode_returns.append(episode_return)
                logging.info(f"Episode return: {episode_return}")
                total_return += episode_return
                logging.info(f"Total return: {total_return}")

            if episode_returns:
                avg_return = total_return / len(episode_returns)
                logging.info(f"Computed average return: {avg_return} from {len(episode_returns)} episodes")
                self.last_avg_return = avg_return  # Store for future reference
                return avg_return
            else:
                logging.warning("No episodes completed during evaluation")
                # Return a small positive value instead of 0 to avoid stopping training
                default_return = 0.1
                self.last_avg_return = default_return
                return default_return

        except Exception as e:
            logging.error(f"Error during evaluation: {e}")
            import traceback
            logging.error(traceback.format_exc())
            # Return a small positive value instead of 0 to avoid stopping training
            default_return = 0.1
            self.last_avg_return = default_return
            return default_return

# # For debugging, replace the main block with this:
# if __name__ == "__main__":
#     # Initialize the environment
#     env = TSNGCLEnvironment(port=5555)

#     try:
#         print("Starting TSNGCLTrainer...")

#         # Create the PPO agent
#         agent, tf_env = create_ppo_agent(env)

#         # Create and run the trainer
#         trainer = TSNGCLTrainer(env, agent, tf_env, num_iterations=1000, collect_steps_per_iteration=100)
#         returns = trainer.run_training()

#         print("Training completed.")

#     except KeyboardInterrupt:
#         print("\nTraining interrupted by user.")
#     finally:
#         env.close()
#         logging.info("Environment closed.")

if __name__ == "__main__":
    # Use TSNGCLTrainer for more comprehensive training
    print("Starting TSN GCL Environment with TSNGCLTrainer")
    logging.info("Starting TSN GCL Environment with TSNGCLTrainer")

    # Clean up any existing ZMQ context
    try:
        import zmq
        zmq.Context.instance().term()
        time.sleep(2)
        zmq._context_initialized = False
        print("Cleaned up existing ZMQ context")
        logging.info("Cleaned up existing ZMQ context")
    except Exception as e:
        print(f"Error cleaning up ZMQ context: {e}")
        logging.error(f"Error cleaning up ZMQ context: {e}")

    # Create environment with robust error handling
    max_attempts = 5
    env = None

    for attempt in range(max_attempts):
        try:
            print(f"Connection attempt {attempt+1}/{max_attempts}")
            logging.info(f"Connection attempt {attempt+1}/{max_attempts}")

            # Create environment
            env = TSNGCLEnvironment(port=5555)

            # Force data_active to True
            env.data_active = True
            env.last_data_received_time = time.time()

            print(f"Connection attempt {attempt+1} successful")
            logging.info(f"Connection attempt {attempt+1} successful")

            # Wait for socket to initialize
            time.sleep(2)
            break
        except Exception as e:
            print(f"Error during connection attempt {attempt+1}: {e}")
            logging.error(f"Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None
            time.sleep(5)

    if env is None:
        print("Failed to create environment after multiple attempts")
        logging.error("Failed to create environment after multiple attempts")
        import sys
        sys.exit(1)

    try:
        # Create a PPO agent
        print("Creating PPO agent...")
        logging.info("Creating PPO agent...")

        agent, tf_env = create_ppo_agent(env)

        # Create TSNGCLTrainer with appropriate parameters
        print("Creating TSNGCLTrainer...")
        logging.info("Creating TSNGCLTrainer...")

        # Use TSNGCLTrainer with more iterations and steps for better training
        trainer = TSNGCLTrainer(
            env=env,
            agent=agent,
            tf_env=tf_env,
            num_iterations=30,  # More iterations for better convergence
            collect_steps_per_iteration=150  # More steps per iteration for better data collection
        )

        # Set early stopping parameters
        trainer.patience = 5  # Stop if no improvement for 5 iterations
        trainer.min_delta_loss = 0.01  # Minimum improvement in loss
        trainer.min_delta_return = 0.1  # Minimum improvement in return

        print("Starting training with TSNGCLTrainer...")
        logging.info("Starting training with TSNGCLTrainer...")

        # Log debug information before starting training
        logging.info("Preparing to start training")
        logging.debug(f"Expected queues: {env.expected_queues}")
        logging.debug(f"Current queue states: {env.queue_states}")
        logging.debug(f"Current batch sizes: {[len(env.state_batch[q]) for q in env.expected_queues]}")
        logging.debug(f"Data active: {env.data_active}")
        logging.debug(f"Last data received time: {env.last_data_received_time}")

        # Log information about time-based batch processing
        logging.info("Time-based batch processing configuration:")
        logging.info(f"Batch processing interval: 200 microseconds")
        current_time_us = int(time.time() * 1000000)
        for queue_id in env.expected_queues:
            if queue_id in env.last_batch_process_time:
                time_since_last_process = current_time_us - env.last_batch_process_time[queue_id]
                logging.debug(f"Queue {queue_id}: {time_since_last_process} microseconds since last batch processing")

        # Force some batch processing to ensure the mechanism works
        logging.info("Forcing initial batch processing for testing")
        for queue_id in env.expected_queues:
            # Create some test data
            test_data = [
                {"num_packets": 1.0, "queue_occupancy": -1.0, "packetArrivalRate": 100.0, "deadline": 0.0, "current_sim_time": 200.0},
                {"num_packets": 2.0, "queue_occupancy": -2.0, "packetArrivalRate": 200.0, "deadline": 0.0, "current_sim_time": 201.0}
            ]
            env._handle_batch_data(queue_id, test_data)
        logging.info("Initial batch processing complete")

        # Run the training loop
        print("Starting training...")
        logging.info("Starting training loop")
        returns = trainer.run_training()
        print("Training completed")
        logging.info("Training loop completed")

        # Print training results
        if returns and len(returns) > 0:
            print(f"Training completed with final return: {returns[-1]}")
            print(f"Best return during training: {max(returns)}")
            logging.info(f"Training completed with final return: {returns[-1]}")
            logging.info(f"Best return during training: {max(returns)}")
        else:
            print("Training completed but no returns were recorded")
            logging.warning("Training completed but no returns were recorded")

    except KeyboardInterrupt:
        print("\nTraining interrupted by user.")
        logging.info("Training interrupted by user.")
    except Exception as e:
        print(f"Error during training: {e}")
        logging.error(f"Error during training: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
    finally:
        # Make sure to close the environment
        if env:
            print("Closing environment...")
            logging.info("Closing environment...")
            env.close()
            print("Environment closed.")
            logging.info("Environment closed.")

