# Prioritized Experience Replay for TensorFlow Agents

This implementation provides a prioritized experience replay buffer for TensorFlow Agents, which can be used as a drop-in replacement for the standard `TFUniformReplayBuffer`.

## Overview

Prioritized Experience Replay (PER) is a technique that improves the efficiency of experience replay by prioritizing experiences based on their TD error. This allows the agent to learn more effectively from experiences that have a higher learning potential.

The implementation is based on the paper [Prioritized Experience Replay](https://arxiv.org/abs/1511.05952) by <PERSON><PERSON><PERSON> et al.

## Features

- Drop-in replacement for `TFUniformReplayBuffer`
- Configurable prioritization exponent (alpha)
- Configurable importance sampling correction (beta)
- Automatic annealing of beta from initial to final value
- Compatible with existing TensorFlow Agents code

## Implementation Details

The `TFPrioritizedReplayBuffer` class extends `TFUniformReplayBuffer` and adds the following features:

1. **Priority Storage**: Maintains a separate table to store priorities for each experience
2. **Prioritized Sampling**: Samples experiences based on their priorities
3. **Importance Sampling**: Corrects the bias introduced by prioritized sampling
4. **Priority Updates**: Allows updating priorities based on TD errors

## Usage

### Basic Usage

```python
from tf_prioritized_replay_buffer import TFPrioritizedReplayBuffer

# Create a prioritized replay buffer
replay_buffer = TFPrioritizedReplayBuffer(
    data_spec=agent.collect_data_spec,
    batch_size=tf_env.batch_size,
    max_length=10000,
    alpha=0.6,  # Prioritization exponent
    beta=0.4,   # Initial importance sampling correction
    epsilon=1e-6  # Small constant to ensure non-zero priority
)
```

### Integration with Training Loop

```python
# Sample from the buffer
dataset = replay_buffer.as_dataset(
    num_parallel_calls=3,
    sample_batch_size=64,
    num_steps=2
).prefetch(3)

# Get experience and buffer info
iterator = iter(dataset)
experience, buffer_info = next(iterator)

# Calculate importance sampling weights
N = replay_buffer.num_frames()
probs = buffer_info.probabilities
importance_weights = tf.pow(N * probs, -beta)
importance_weights = importance_weights / tf.reduce_max(importance_weights)

# Train with importance sampling weights
train_loss = agent.train(experience, weights=importance_weights)

# Update priorities based on TD error
td_errors = tf.abs(train_loss.extra.td_loss)
new_priorities = td_errors + epsilon
replay_buffer.update_priorities(buffer_info.ids, new_priorities)
```

### Annealing Beta

It's recommended to anneal beta from an initial value (e.g., 0.4) to 1.0 over the course of training:

```python
# Update beta for importance sampling (anneal from beta_start to beta_end)
progress = min(1.0, iteration / num_iterations)
current_beta = beta_start + progress * (beta_end - beta_start)
replay_buffer.set_beta(current_beta)
```

## Parameters

- **alpha**: The prioritization exponent (α). Controls how much prioritization is used, with α=0 corresponding to uniform sampling. Default: 1.0
- **beta**: The importance sampling exponent (β). Controls how much importance sampling is used to correct for the bias introduced by prioritized sampling. Default: 0.5
- **epsilon**: Small positive constant added to priorities to ensure non-zero sampling probability. Default: 1e-6

## Example

See `example_prioritized_replay.py` for a complete example of how to use the prioritized experience replay buffer with the TSNGCLEnvironment.

## Benefits of Prioritized Experience Replay

1. **Faster Learning**: By focusing on experiences with higher TD errors, the agent can learn more efficiently
2. **Better Sample Efficiency**: Makes better use of the replay buffer by sampling more informative experiences
3. **Improved Performance**: Often leads to better final performance, especially in environments with sparse rewards

## Recommended Hyperparameters

Based on the original paper and empirical results, the following hyperparameters are recommended:

- **alpha**: 0.6 (moderate prioritization)
- **beta_start**: 0.4 (initial importance sampling correction)
- **beta_end**: 1.0 (final importance sampling correction)
- **epsilon**: 1e-6 (small constant to ensure non-zero priority)

## References

1. Schaul, T., Quan, J., Antonoglou, I., & Silver, D. (2015). Prioritized Experience Replay. arXiv preprint arXiv:1511.05952.
2. TensorFlow Agents: https://www.tensorflow.org/agents
