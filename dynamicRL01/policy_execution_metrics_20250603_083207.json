{"execution_summary": {"start_time": "2025-06-03 08:31:51", "end_time": "2025-06-03 08:32:07", "duration_seconds": 16.283382654190063, "duration_minutes": 0.2713897109031677, "duration_hours": 0.004523161848386129}, "performance_metrics": {"total_actions_sent": 0, "total_observations_processed": 21200, "total_data_rejections": 0, "connection_resets": 0, "actions_per_second": 0.0, "observations_per_second": 1301.9407852916104, "data_rejection_rate": 0.0}, "configuration": {"batch_interval_us": 5000, "action_rate_multiplier": 10, "max_data_age_us": 10000, "effective_action_interval_ms": 50.0}, "model_information": {"session_dir": "saved_models/session_20250602_214659_final", "optuna_metadata": {"trial_number": 49, "best_value": 50154.00921016399, "total_trials": 57, "source_file": "optuna_results_improved.json"}, "detailed_metrics": {"training_duration_seconds": 142.05453372001648, "training_start_time": "2025-06-02 21:44:37", "training_end_time": "2025-06-02 21:46:59", "returns": [49.549472874403, 48.02982981801033, 48.44146261811257, 28.12698102593422, 47.064953124523164, 46.95793083906174, 46.40611698031425, 46.75196053385734, 45.97543831467628, 46.21136776208878], "final_return": 46.21136776208878, "best_return": 49.549472874403, "worst_return": 28.12698102593422, "mean_return": 45.35155138909818, "return_std": 5.837354449000248, "total_iterations": 10, "hyperparameters_used": {"learning_rate": 0.0005831605096373321, "n_layers": 4, "layer_size": 256, "activation_fn": "elu", "entropy_regularization": 0.011423932540995442, "importance_ratio_clipping": 0.22775741315763118, "num_epochs": 5, "lambda_value": 0.9563059741267267, "discount_factor": 0.9655781441740209, "collect_steps_per_iteration": 200, "num_iterations": 20}, "optuna_metadata": {"trial_number": 49, "best_value": 50154.00921016399, "total_trials": 57, "source_file": "optuna_results_improved.json"}, "loss_explosion_occurred": false, "learning_rate_scheduler_used": true, "zmq_reset_performed": true}, "model_return": 49.549472874403}, "environment_stats": {"expected_queues": ["S0.eth[2].macLayer.queue.queue[0]", "S0.eth[2].macLayer.queue.queue[1]", "S0.eth[2].macLayer.queue.queue[2]", "S1.eth[1].macLayer.queue.queue[0]", "S1.eth[1].macLayer.queue.queue[1]", "S1.eth[1].macLayer.queue.queue[2]", "S2.eth[1].macLayer.queue.queue[0]", "S2.eth[1].macLayer.queue.queue[1]", "S2.eth[1].macLayer.queue.queue[2]"], "num_queues": 9}}