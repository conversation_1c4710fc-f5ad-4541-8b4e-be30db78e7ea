# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/

# Virtual environments
venv/
env/
ENV/

# Logs
*.log
app.log

# Jupyter Notebook
.ipynb_checkpoints

# TensorFlow checkpoints and saved models
*.h5
*.ckpt
*.pb
checkpoint

# Project specific files
*.csv
sampleRes.csv
sampleResProcessed.csv
.~lock.sampleRes.csv#
dataHandle.zip

# IDE specific files
.vscode/
.idea/