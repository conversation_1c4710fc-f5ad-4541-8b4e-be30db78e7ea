import optuna
import os
import sys
import sqlite3

def reset_study(db_path, study_name):
    """Reset an Optuna study by deleting it and creating a new one."""
    try:
        # Check if the database file exists
        if not os.path.exists(db_path):
            print(f"Database file {db_path} does not exist.")
            return False
        
        # Connect to the database
        storage_name = f"sqlite:///{db_path}"
        print(f"Connecting to {storage_name}")
        
        # Try to delete the study
        try:
            optuna.delete_study(study_name=study_name, storage=storage_name)
            print(f"Study '{study_name}' deleted successfully.")
        except Exception as e:
            print(f"Error deleting study: {e}")
            print("This might be normal if the study doesn't exist yet.")
        
        # Create a new study
        pruner = optuna.pruners.MedianPruner(
            n_startup_trials=5,
            n_warmup_steps=10,
            interval_steps=1
        )
        
        study = optuna.create_study(
            study_name=study_name,
            storage=storage_name,
            load_if_exists=False,  # Create a new study
            direction="maximize",
            pruner=pruner
        )
        
        print(f"New study '{study_name}' created successfully.")
        return True
    except Exception as e:
        print(f"Error resetting study: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python reset_optuna_db.py <db_path> <study_name>")
        sys.exit(1)
    
    db_path = sys.argv[1]
    study_name = sys.argv[2]
    
    # Confirm with the user
    print(f"This will delete the study '{study_name}' in database '{db_path}' and create a new one.")
    confirm = input("Are you sure you want to continue? (y/n): ")
    
    if confirm.lower() == 'y':
        success = reset_study(db_path, study_name)
        if success:
            print("Study reset successfully.")
        else:
            print("Failed to reset study.")
    else:
        print("Operation cancelled.")
