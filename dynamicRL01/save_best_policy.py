"""
Save the best trained policy for production use.

This script loads the best parameters from Optuna optimization,
creates a PPO agent with those parameters, and saves just the policy
for production use using TF-Agents PolicySaver.
"""

import json
import logging
import os
import sys
import time
import tensorflow as tf
from revamped_new import TSNGCLEnvironment, create_ppo_agent
from tf_agents.policies import policy_saver

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    filename='save_policy.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

def load_best_params(file_path="best_params_improved.txt"):
    """Load the best parameters from the file."""
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found. Run Optuna optimization first.")
        logging.error(f"Error: {file_path} not found. Run Optuna optimization first.")
        return None

    # Parse the best parameters file
    best_params = {}
    with open(file_path, 'r') as f:
        lines = f.readlines()
        for line in lines[2:]:  # Skip the first two lines (trial number and value)
            if ':' in line:
                key, value = line.strip().split(':', 1)
                key = key.strip()
                value = value.strip()

                # Convert value to appropriate type
                if key == 'n_layers' or key == 'num_epochs':
                    best_params[key] = int(value)
                elif key == 'layer_size':
                    best_params[key] = int(value)
                elif key == 'activation_fn':
                    best_params[key] = value
                else:
                    try:
                        best_params[key] = float(value)
                    except ValueError:
                        best_params[key] = value

    return best_params

def main():
    """Save the best policy for production use."""
    print("Saving best policy for production use")
    logging.info("Saving best policy for production use")

    # Load the best parameters
    best_params_file = input("Enter the path to the best parameters file (default: best_params_improved.txt): ") or "best_params_improved.txt"
    best_params = load_best_params(best_params_file)

    if best_params is None:
        return 1

    print(f"Loaded best parameters from {best_params_file}:")
    for key, value in best_params.items():
        print(f"  {key}: {value}")

    # Clean up any existing ZMQ context
    try:
        import zmq
        zmq.Context.instance().term()
        time.sleep(2)
        zmq._context_initialized = False
        print("Cleaned up existing ZMQ context")
        logging.info("Cleaned up existing ZMQ context")
    except Exception as e:
        print(f"Error cleaning up ZMQ context: {e}")
        logging.error(f"Error cleaning up ZMQ context: {e}")

    # Create environment with robust error handling
    max_attempts = 5
    env = None

    for attempt in range(max_attempts):
        try:
            print(f"Connection attempt {attempt+1}/{max_attempts}")
            logging.info(f"Connection attempt {attempt+1}/{max_attempts}")

            # Create environment
            env = TSNGCLEnvironment(port=5555)

            print(f"Connection attempt {attempt+1} successful")
            print("Environment initialized - waiting for real data from OMNeT++")
            logging.info(f"Connection attempt {attempt+1} successful")
            logging.info("Environment initialized - waiting for real data from OMNeT++")

            # Wait for socket to initialize
            time.sleep(2)
            break
        except Exception as e:
            print(f"Error during connection attempt {attempt+1}: {e}")
            logging.error(f"Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None
            time.sleep(5)

    if env is None:
        print("Failed to create environment after multiple attempts")
        logging.error("Failed to create environment after multiple attempts")
        return 1

    try:
        # Prepare parameters for agent creation
        learning_rate = best_params.get('learning_rate', 5e-5)

        # Create fc_layer_params based on n_layers and layer_size
        n_layers = best_params.get('n_layers', 2)
        layer_size = best_params.get('layer_size', 64)
        fc_layer_params = tuple([layer_size] * n_layers)

        # Map activation function
        activation_name = best_params.get('activation_fn', 'relu')
        activation_fn_map = {
            'relu': tf.keras.activations.relu,
            'elu': tf.keras.activations.elu,
            'tanh': tf.keras.activations.tanh,
            'swish': tf.nn.swish
        }
        activation_fn = activation_fn_map.get(activation_name, tf.keras.activations.relu)

        # Create the agent with best parameters
        agent, tf_env = create_ppo_agent(
            env,
            learning_rate=learning_rate,
            fc_layer_params=fc_layer_params,
            activation_fn=activation_fn,
            entropy_regularization=best_params.get('entropy_regularization', 0.01),
            importance_ratio_clipping=best_params.get('importance_ratio_clipping', 0.2),
            num_epochs=best_params.get('num_epochs', 3)
        )

        # Create a train step variable to track training iterations
        train_step = tf.Variable(0, dtype=tf.int64, name='train_step')
        
        # Create a policy saver
        print("Creating policy saver using SavedModel...")
        logging.info("Creating policy saver using SavedModel...")
        
        # Create directory for saved policies if it doesn't exist
        os.makedirs("saved_policies", exist_ok=True)
        policy_dir = os.path.join("saved_policies", "production_policy")

        # Create a simplified version of the policy for saving
        print("Creating simplified policy for saving...")
        logging.info("Creating simplified policy for saving...")

        # Extract the actor network from the policy
        policy_network = agent.policy.wrapped_policy._actor_network

        # Save the underlying Keras model
        try:
            # Try to access the underlying Keras model
            keras_model = policy_network._encoder
            keras_model.save(os.path.join(policy_dir, "actor_model"))
            print(f"Actor model successfully saved to {os.path.join(policy_dir, 'actor_model')}")
            logging.info(f"Actor model successfully saved to {os.path.join(policy_dir, 'actor_model')}")
        except AttributeError:
            # If that fails, try to save the network directly
            tf.saved_model.save(policy_network, os.path.join(policy_dir, "actor_network"))
            print(f"Actor network successfully saved to {os.path.join(policy_dir, 'actor_network')}")
            logging.info(f"Actor network successfully saved to {os.path.join(policy_dir, 'actor_network')}")

        # Save metadata about the policy
        metadata = {
            "parameters": best_params,
            "save_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "description": "Best policy from Optuna optimization"
        }
        
        with open(os.path.join(policy_dir, "metadata.json"), "w") as f:
            json.dump(metadata, f, indent=2)
        
        print(f"Policy metadata saved to {os.path.join(policy_dir, 'metadata.json')}")
        logging.info(f"Policy metadata saved to {os.path.join(policy_dir, 'metadata.json')}")

    except Exception as e:
        print(f"Error saving policy: {e}")
        logging.error(f"Error saving policy: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
        return 1
    finally:
        # Make sure to close the environment
        if env:
            print("Closing environment...")
            logging.info("Closing environment...")
            env.close()
            print("Environment closed.")
            logging.info("Environment closed.")

    return 0

if __name__ == "__main__":
    sys.exit(main())
