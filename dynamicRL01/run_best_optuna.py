import json
import logging
import os
import sys
import time
import numpy as np
import tensorflow as tf
import multiprocessing
from revamped_new import TSNGCLEnvironment, create_ppo_agent, TSNGCLTrainer

# use WRAPT_DISABLE_EXTENSIONS=true before running this code

# Configure multiprocessing to use spawn method which is more stable during cleanup
if sys.platform != 'win32':  # Not needed on Windows
    multiprocessing.set_start_method('spawn', force=True)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    filename='best_agent.log',
    format='%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    filemode='a'
)

def load_best_params(file_path="optuna_results_improved.json"):
    """Load the best parameters from the improved Optuna results JSON file."""
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found. Run Optuna optimization first.")
        logging.error(f"Error: {file_path} not found. Run Optuna optimization first.")
        return None, None

    try:
        with open(file_path, 'r') as f:
            results = json.load(f)

        # Extract best trial information
        best_trial = results.get('best_trial', {})
        best_params = best_trial.get('params', {})
        best_value = best_trial.get('value', None)
        trial_number = best_trial.get('number', None)

        if not best_params:
            print(f"Error: No best parameters found in {file_path}")
            logging.error(f"Error: No best parameters found in {file_path}")
            return None, None

        # Add metadata
        metadata = {
            'trial_number': trial_number,
            'best_value': best_value,
            'total_trials': len(results.get('trials', [])),
            'source_file': file_path
        }

        return best_params, metadata

    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file {file_path}: {e}")
        logging.error(f"Error parsing JSON file {file_path}: {e}")
        return None, None
    except Exception as e:
        print(f"Error loading parameters from {file_path}: {e}")
        logging.error(f"Error loading parameters from {file_path}: {e}")
        return None, None

def save_model(agent, iteration=None, best_params=None, returns=None, metadata=None, detailed_metrics=None):
    """Saves the agent's policy with proper TF-Agents serialization and detailed metrics."""
    model_dir = "saved_models"
    os.makedirs(model_dir, exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    if isinstance(iteration, int):
        session_dir = os.path.join(model_dir, f"session_{timestamp}_iter_{iteration}")
    else:
        session_dir = os.path.join(model_dir, f"session_{timestamp}_{iteration}")

    os.makedirs(session_dir, exist_ok=True)

    try:
        # Instead of using PolicySaver, save the underlying networks directly
        # 1. Save the actor network
        actor_dir = os.path.join(session_dir, "actor_network")
        os.makedirs(actor_dir, exist_ok=True)

        # Get the actor network from the agent
        actor_network = agent._actor_net
        # Save the actor network using TensorFlow's SavedModel format
        tf.saved_model.save(actor_network, actor_dir)
        print(f"Actor network saved to {actor_dir}")

        # 2. Save the value network
        value_dir = os.path.join(session_dir, "value_network")
        os.makedirs(value_dir, exist_ok=True)

        # Get the value network from the agent
        value_network = agent._value_net
        # Save the value network using TensorFlow's SavedModel format
        tf.saved_model.save(value_network, value_dir)
        print(f"Value network saved to {value_dir}")

        # 3. Save additional training artifacts
        if best_params:
            with open(os.path.join(session_dir, "params.json"), 'w') as f:
                json.dump(best_params, f, indent=4)

        # Save metadata from Optuna optimization
        if metadata:
            with open(os.path.join(session_dir, "optuna_metadata.json"), 'w') as f:
                json.dump(metadata, f, indent=4)

        # Save detailed metrics
        if detailed_metrics:
            with open(os.path.join(session_dir, "detailed_metrics.json"), 'w') as f:
                json.dump(detailed_metrics, f, indent=4)

        if returns:
            with open(os.path.join(session_dir, "returns.json"), 'w') as f:
                json.dump(returns, f, indent=4)

            try:
                import matplotlib.pyplot as plt
                plt.figure(figsize=(10, 6))
                plt.plot(returns)
                plt.title("Training Returns")
                plt.xlabel("Iteration")
                plt.ylabel("Return")
                plt.grid(True)
                plt.savefig(os.path.join(session_dir, "returns.png"))
                plt.close()
            except Exception as e:
                print(f"Couldn't save returns plot: {e}")

        # 4. Save the environment specs for future loading
        try:
            specs = {
                'observation_spec': str(agent.time_step_spec().observation),
                'action_spec': str(agent.action_spec()),
                'time_step_spec': str(agent.time_step_spec())
            }
            with open(os.path.join(session_dir, "specs.json"), 'w') as f:
                json.dump(specs, f, indent=4)
        except Exception as e:
            print(f"Could not save specs: {e}")

        print(f"Model successfully saved to {session_dir}")
        return session_dir

    except Exception as e:
        print(f"Error saving model: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

class LossAwareTrainer(TSNGCLTrainer):
    """Enhanced trainer with loss explosion detection and user interaction."""

    def __init__(self, env, agent, tf_env, num_iterations=30, collect_steps_per_iteration=150, lr_scheduler=None):
        super().__init__(env, agent, tf_env, num_iterations, collect_steps_per_iteration, lr_scheduler)
        self.save_model_callback = None  # Will be set by main function

    def set_save_callback(self, save_callback):
        """Set the callback function for saving models."""
        self.save_model_callback = save_callback

    def train_step(self, experience):
        """Enhanced train step with loss explosion detection."""
        # Call the parent train_step method
        train_loss = super().train_step(experience)

        # Handle the case where train_step returns a string signal
        if isinstance(train_loss, str):
            if train_loss == "SAVE_AND_STOP":
                print("🛑 Loss explosion detected - saving model and stopping training")
                logging.info("Loss explosion detected - saving model and stopping training")

                # Save the model using the callback if available
                if self.save_model_callback:
                    saved_path = self.save_model_callback(iteration="loss_explosion",
                                                        returns=getattr(self, 'current_returns', []))
                    if saved_path:
                        print(f"💾 Model saved to {saved_path}")
                        logging.info(f"Model saved to {saved_path}")

                return "SAVE_AND_STOP"
            elif train_loss is None:
                # Buffer was reset, continue training
                print("🔄 Experience buffer reset - continuing with fresh data")
                logging.info("Experience buffer reset - continuing with fresh data")
                return None

        return train_loss

def create_comprehensive_plots(returns, trainer, detailed_metrics, best_params):
    """Create comprehensive plots to assess model efficacy."""
    import matplotlib.pyplot as plt
    import matplotlib.gridspec as gridspec
    from scipy import stats
    import seaborn as sns

    # Set style for better-looking plots
    plt.style.use('default')
    sns.set_palette("husl")

    # Create plots directory if it doesn't exist
    os.makedirs("plots", exist_ok=True)

    print("📊 Creating comprehensive model efficacy plots...")
    logging.info("Creating comprehensive model efficacy plots...")

    # Get losses from trainer if available
    losses = getattr(trainer, 'losses', [])

    # 1. MAIN DASHBOARD - Overview of key metrics
    fig = plt.figure(figsize=(20, 12))
    gs = gridspec.GridSpec(3, 4, figure=fig, hspace=0.3, wspace=0.3)

    # Returns over time
    ax1 = fig.add_subplot(gs[0, :2])
    ax1.plot(returns, 'b-', linewidth=2, label='Returns')
    ax1.axhline(y=np.mean(returns), color='r', linestyle='--', alpha=0.7, label=f'Mean: {np.mean(returns):.3f}')
    ax1.fill_between(range(len(returns)), returns, alpha=0.3)
    ax1.set_title('Returns Over Time', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Iteration')
    ax1.set_ylabel('Return')
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # Loss progression (if available)
    if losses:
        ax2 = fig.add_subplot(gs[0, 2:])
        ax2.plot(losses, 'r-', linewidth=2, label='Training Loss')
        ax2.axhline(y=np.mean(losses), color='b', linestyle='--', alpha=0.7, label=f'Mean: {np.mean(losses):.3f}')
        ax2.set_title('Loss Progression', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Training Step')
        ax2.set_ylabel('Loss')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_yscale('log')  # Log scale for better loss visualization

    # Return distribution
    ax3 = fig.add_subplot(gs[1, 0])
    ax3.hist(returns, bins=min(20, len(returns)//2), alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(x=np.mean(returns), color='red', linestyle='--', label=f'Mean: {np.mean(returns):.3f}')
    ax3.axvline(x=np.median(returns), color='green', linestyle='--', label=f'Median: {np.median(returns):.3f}')
    ax3.set_title('Return Distribution', fontsize=12, fontweight='bold')
    ax3.set_xlabel('Return Value')
    ax3.set_ylabel('Frequency')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Performance metrics summary
    ax4 = fig.add_subplot(gs[1, 1])
    metrics_data = {
        'Best': max(returns),
        'Worst': min(returns),
        'Mean': np.mean(returns),
        'Std': np.std(returns),
        'Final': returns[-1]
    }
    bars = ax4.bar(metrics_data.keys(), metrics_data.values(),
                   color=['green', 'red', 'blue', 'orange', 'purple'], alpha=0.7)
    ax4.set_title('Performance Summary', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Return Value')
    ax4.grid(True, alpha=0.3)
    # Add value labels on bars
    for bar, value in zip(bars, metrics_data.values()):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=10)

    # Learning progress (improvement over time)
    ax5 = fig.add_subplot(gs[1, 2])
    if len(returns) > 1:
        # Calculate rolling average to show learning trend
        window_size = max(3, len(returns) // 5)
        rolling_avg = np.convolve(returns, np.ones(window_size)/window_size, mode='valid')
        ax5.plot(range(window_size-1, len(returns)), rolling_avg, 'g-', linewidth=3, label=f'Rolling Avg (window={window_size})')
        ax5.plot(returns, 'b-', alpha=0.5, label='Raw Returns')
        ax5.set_title('Learning Progress', fontsize=12, fontweight='bold')
        ax5.set_xlabel('Iteration')
        ax5.set_ylabel('Return')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

    # Stability analysis
    ax6 = fig.add_subplot(gs[1, 3])
    if len(returns) > 5:
        # Calculate moving variance to show stability
        window_size = max(3, len(returns) // 4)
        moving_var = []
        for i in range(window_size, len(returns) + 1):
            moving_var.append(np.var(returns[i-window_size:i]))
        ax6.plot(range(window_size, len(returns) + 1), moving_var, 'purple', linewidth=2)
        ax6.set_title('Learning Stability', fontsize=12, fontweight='bold')
        ax6.set_xlabel('Iteration')
        ax6.set_ylabel('Moving Variance')
        ax6.grid(True, alpha=0.3)

    # Loss vs Returns correlation (if losses available)
    if losses and len(losses) >= len(returns):
        ax7 = fig.add_subplot(gs[2, 0])
        # Align losses with returns (take every nth loss to match returns)
        step = len(losses) // len(returns) if len(losses) > len(returns) else 1
        aligned_losses = losses[::step][:len(returns)]
        ax7.scatter(aligned_losses, returns, alpha=0.6, c=range(len(returns)), cmap='viridis')
        ax7.set_title('Loss vs Returns', fontsize=12, fontweight='bold')
        ax7.set_xlabel('Loss')
        ax7.set_ylabel('Return')
        ax7.grid(True, alpha=0.3)
        # Add correlation coefficient
        if len(aligned_losses) == len(returns):
            corr, _ = stats.pearsonr(aligned_losses, returns)
            ax7.text(0.05, 0.95, f'Correlation: {corr:.3f}', transform=ax7.transAxes,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    # Training efficiency
    ax8 = fig.add_subplot(gs[2, 1])
    if len(returns) > 1:
        improvements = [returns[i] - returns[i-1] for i in range(1, len(returns))]
        ax8.bar(range(1, len(returns)), improvements,
               color=['green' if x > 0 else 'red' for x in improvements], alpha=0.7)
        ax8.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax8.set_title('Iteration-to-Iteration Improvement', fontsize=12, fontweight='bold')
        ax8.set_xlabel('Iteration')
        ax8.set_ylabel('Return Change')
        ax8.grid(True, alpha=0.3)

    # Hyperparameter impact visualization
    ax9 = fig.add_subplot(gs[2, 2:])
    param_names = list(best_params.keys())[:8]  # Show top 8 parameters
    param_values = [best_params[name] for name in param_names]

    # Normalize values for better visualization
    normalized_values = []
    for val in param_values:
        if isinstance(val, (int, float)):
            normalized_values.append(float(val))
        else:
            normalized_values.append(hash(str(val)) % 100)  # Convert non-numeric to numeric

    bars = ax9.barh(param_names, normalized_values, color='lightcoral', alpha=0.7)
    ax9.set_title('Hyperparameter Configuration', fontsize=12, fontweight='bold')
    ax9.set_xlabel('Normalized Value')
    ax9.grid(True, alpha=0.3)

    # Add actual values as text
    for i, (bar, actual_val) in enumerate(zip(bars, param_values)):
        ax9.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                str(actual_val), ha='left', va='center', fontsize=9)

    plt.suptitle('Model Efficacy Dashboard - Comprehensive Analysis', fontsize=16, fontweight='bold')
    plt.savefig('plots/model_efficacy_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ Model efficacy dashboard saved to plots/model_efficacy_dashboard.png")
    logging.info("Model efficacy dashboard saved to plots/model_efficacy_dashboard.png")

    # 2. DETAILED ANALYSIS PLOTS
    create_detailed_analysis_plots(returns, losses, detailed_metrics, best_params)

    # 3. LEARNING DYNAMICS PLOTS
    create_learning_dynamics_plots(returns, losses)

    # 4. PERFORMANCE COMPARISON PLOTS
    create_performance_comparison_plots(returns, detailed_metrics)

    print("📊 All comprehensive plots created successfully!")
    logging.info("All comprehensive plots created successfully!")

def create_detailed_analysis_plots(returns, losses, detailed_metrics, best_params):
    """Create detailed analysis plots for deeper insights."""
    import matplotlib.pyplot as plt

    # Statistical Analysis Plot
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # 1. Return trend analysis with confidence intervals
    if len(returns) > 5:
        from scipy import stats
        x = np.arange(len(returns))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, returns)
        line = slope * x + intercept

        ax1.plot(returns, 'bo-', alpha=0.6, label='Actual Returns')
        ax1.plot(x, line, 'r-', label=f'Trend (R²={r_value**2:.3f})')

        # Add confidence interval
        residuals = returns - line
        mse = np.mean(residuals**2)
        confidence = 1.96 * np.sqrt(mse)  # 95% confidence interval
        ax1.fill_between(x, line - confidence, line + confidence, alpha=0.2, color='red', label='95% CI')

        ax1.set_title('Return Trend Analysis', fontweight='bold')
        ax1.set_xlabel('Iteration')
        ax1.set_ylabel('Return')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

    # 2. Loss analysis (if available)
    if losses:
        ax2.plot(losses, 'r-', alpha=0.7, label='Training Loss')

        # Add exponential moving average
        alpha = 0.1
        ema = [losses[0]]
        for i in range(1, len(losses)):
            ema.append(alpha * losses[i] + (1 - alpha) * ema[-1])
        ax2.plot(ema, 'b-', linewidth=2, label='EMA')

        ax2.set_title('Loss Analysis with EMA', fontweight='bold')
        ax2.set_xlabel('Training Step')
        ax2.set_ylabel('Loss')
        ax2.set_yscale('log')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

    # 3. Performance consistency analysis
    if len(returns) > 10:
        # Calculate rolling statistics
        window = max(5, len(returns) // 4)
        rolling_mean = []
        rolling_std = []
        for i in range(window, len(returns) + 1):
            window_data = returns[i-window:i]
            rolling_mean.append(np.mean(window_data))
            rolling_std.append(np.std(window_data))

        ax3.plot(range(window, len(returns) + 1), rolling_mean, 'g-', label='Rolling Mean')
        ax3.fill_between(range(window, len(returns) + 1),
                        np.array(rolling_mean) - np.array(rolling_std),
                        np.array(rolling_mean) + np.array(rolling_std),
                        alpha=0.3, color='green', label='±1 Std Dev')
        ax3.set_title('Performance Consistency', fontweight='bold')
        ax3.set_xlabel('Iteration')
        ax3.set_ylabel('Return')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

    # 4. Training efficiency metrics
    training_duration = detailed_metrics.get('training_duration_seconds', 0)
    total_iterations = detailed_metrics.get('total_iterations', len(returns))

    efficiency_metrics = {
        'Avg Return': np.mean(returns),
        'Return/Iteration': np.mean(returns) / max(1, total_iterations),
        'Return/Second': np.mean(returns) / max(1, training_duration),
        'Final/Initial': returns[-1] / returns[0] if returns[0] != 0 else 1,
        'Improvement Rate': (returns[-1] - returns[0]) / max(1, len(returns))
    }

    bars = ax4.bar(range(len(efficiency_metrics)), list(efficiency_metrics.values()),
                   color=['blue', 'green', 'orange', 'red', 'purple'], alpha=0.7)
    ax4.set_title('Training Efficiency Metrics', fontweight='bold')
    ax4.set_xticks(range(len(efficiency_metrics)))
    ax4.set_xticklabels(list(efficiency_metrics.keys()), rotation=45, ha='right')
    ax4.set_ylabel('Metric Value')
    ax4.grid(True, alpha=0.3)

    # Add value labels
    for bar, value in zip(bars, efficiency_metrics.values()):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    plt.savefig('plots/detailed_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ Detailed analysis plots saved to plots/detailed_analysis.png")
    logging.info("Detailed analysis plots saved to plots/detailed_analysis.png")

def create_learning_dynamics_plots(returns, losses):
    """Create plots focused on learning dynamics."""
    import matplotlib.pyplot as plt

    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    axes = axes.flatten()

    # 1. Learning curve with phases
    ax = axes[0]
    if len(returns) > 1:
        # Identify learning phases
        third = len(returns) // 3
        early_phase = returns[:third] if third > 0 else returns[:1]
        mid_phase = returns[third:2*third] if third > 0 else []
        late_phase = returns[2*third:] if third > 0 else []

        ax.plot(range(len(early_phase)), early_phase, 'r-', label='Early Learning', linewidth=2)
        if mid_phase:
            ax.plot(range(len(early_phase), len(early_phase) + len(mid_phase)), mid_phase, 'b-', label='Mid Learning', linewidth=2)
        if late_phase:
            ax.plot(range(len(early_phase) + len(mid_phase), len(returns)), late_phase, 'g-', label='Late Learning', linewidth=2)

        ax.set_title('Learning Phases', fontweight='bold')
        ax.set_xlabel('Iteration')
        ax.set_ylabel('Return')
        ax.legend()
        ax.grid(True, alpha=0.3)

    # 2. Learning rate analysis
    ax = axes[1]
    if len(returns) > 2:
        learning_rates = []
        for i in range(1, len(returns)):
            rate = (returns[i] - returns[i-1]) / max(abs(returns[i-1]), 0.001)
            learning_rates.append(rate)

        ax.plot(learning_rates, 'purple', linewidth=2)
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax.set_title('Learning Rate (% Change)', fontweight='bold')
        ax.set_xlabel('Iteration')
        ax.set_ylabel('% Change in Return')
        ax.grid(True, alpha=0.3)

    # 3. Convergence analysis
    ax = axes[2]
    if len(returns) > 5:
        # Calculate convergence metric (variance of recent returns)
        window = max(3, len(returns) // 5)
        convergence = []
        for i in range(window, len(returns) + 1):
            recent_returns = returns[i-window:i]
            convergence.append(np.std(recent_returns))

        ax.plot(range(window, len(returns) + 1), convergence, 'orange', linewidth=2)
        ax.set_title('Convergence Analysis (Stability)', fontweight='bold')
        ax.set_xlabel('Iteration')
        ax.set_ylabel('Return Std Dev (Lower = More Converged)')
        ax.grid(True, alpha=0.3)

    # 4. Loss dynamics (if available)
    ax = axes[3]
    if losses and len(losses) > 5:
        # Loss acceleration/deceleration
        loss_changes = np.diff(losses)
        loss_acceleration = np.diff(loss_changes)

        ax.plot(loss_acceleration, 'red', alpha=0.7, label='Loss Acceleration')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax.set_title('Loss Dynamics', fontweight='bold')
        ax.set_xlabel('Training Step')
        ax.set_ylabel('Loss Acceleration')
        ax.grid(True, alpha=0.3)

    # 5. Performance volatility
    ax = axes[4]
    if len(returns) > 3:
        # Calculate rolling volatility
        window = max(3, len(returns) // 6)
        volatility = []
        for i in range(window, len(returns) + 1):
            window_returns = returns[i-window:i]
            volatility.append(np.std(window_returns) / np.mean(window_returns) if np.mean(window_returns) != 0 else 0)

        ax.plot(range(window, len(returns) + 1), volatility, 'brown', linewidth=2)
        ax.set_title('Performance Volatility', fontweight='bold')
        ax.set_xlabel('Iteration')
        ax.set_ylabel('Coefficient of Variation')
        ax.grid(True, alpha=0.3)

    # 6. Learning efficiency
    ax = axes[5]
    if len(returns) > 1:
        # Cumulative improvement vs iterations
        cumulative_improvement = np.cumsum([returns[i] - returns[0] for i in range(len(returns))])
        efficiency = cumulative_improvement / np.arange(1, len(returns) + 1)

        ax.plot(efficiency, 'teal', linewidth=2)
        ax.set_title('Learning Efficiency', fontweight='bold')
        ax.set_xlabel('Iteration')
        ax.set_ylabel('Avg Improvement per Iteration')
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('plots/learning_dynamics.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ Learning dynamics plots saved to plots/learning_dynamics.png")
    logging.info("Learning dynamics plots saved to plots/learning_dynamics.png")

def create_performance_comparison_plots(returns, detailed_metrics):
    """Create performance comparison and benchmark plots."""
    import matplotlib.pyplot as plt

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

    # 1. Performance vs baseline comparison
    baseline_return = returns[0] if returns else 0
    improvements = [r - baseline_return for r in returns]

    ax1.bar(range(len(improvements)), improvements,
           color=['green' if x > 0 else 'red' for x in improvements], alpha=0.7)
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax1.set_title('Performance vs Initial Baseline', fontweight='bold')
    ax1.set_xlabel('Iteration')
    ax1.set_ylabel('Improvement over Baseline')
    ax1.grid(True, alpha=0.3)

    # 2. Quartile analysis
    if len(returns) >= 4:
        q1, q2, q3 = np.percentile(returns, [25, 50, 75])
        quartile_data = {
            'Q1 (25%)': q1,
            'Q2 (50%)': q2,
            'Q3 (75%)': q3,
            'Mean': np.mean(returns),
            'Max': max(returns)
        }

        bars = ax2.bar(quartile_data.keys(), quartile_data.values(),
                      color=['lightblue', 'blue', 'darkblue', 'green', 'red'], alpha=0.7)
        ax2.set_title('Performance Quartiles', fontweight='bold')
        ax2.set_ylabel('Return Value')
        ax2.grid(True, alpha=0.3)

        # Add value labels
        for bar, value in zip(bars, quartile_data.values()):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=10)

    # 3. Success rate analysis
    if len(returns) > 1:
        success_threshold = np.mean(returns)  # Define success as above-average performance
        success_rate = sum(1 for r in returns if r >= success_threshold) / len(returns)

        categories = ['Success Rate', 'Failure Rate']
        values = [success_rate, 1 - success_rate]
        colors = ['green', 'red']

        wedges, texts, autotexts = ax3.pie(values, labels=categories, colors=colors, autopct='%1.1f%%', startangle=90)
        ax3.set_title(f'Success Rate (Threshold: {success_threshold:.3f})', fontweight='bold')

    # 4. Training summary metrics
    summary_data = {
        'Duration (s)': detailed_metrics.get('training_duration_seconds', 0),
        'Iterations': detailed_metrics.get('total_iterations', len(returns)),
        'Best Return': detailed_metrics.get('best_return', max(returns) if returns else 0),
        'Final Return': detailed_metrics.get('final_return', returns[-1] if returns else 0),
        'Mean Return': detailed_metrics.get('mean_return', np.mean(returns) if returns else 0)
    }

    # Normalize for visualization
    max_val = max(summary_data.values())
    normalized_data = {k: v/max_val for k, v in summary_data.items()}

    bars = ax4.barh(list(normalized_data.keys()), list(normalized_data.values()),
                   color='skyblue', alpha=0.7)
    ax4.set_title('Training Summary (Normalized)', fontweight='bold')
    ax4.set_xlabel('Normalized Value')
    ax4.grid(True, alpha=0.3)

    # Add actual values as text
    for bar, actual_val in zip(bars, summary_data.values()):
        ax4.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2,
                f'{actual_val:.3f}', ha='left', va='center', fontsize=9)

    plt.tight_layout()
    plt.savefig('plots/performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("✅ Performance comparison plots saved to plots/performance_comparison.png")
    logging.info("Performance comparison plots saved to plots/performance_comparison.png")

def reset_zmq_connection(port=5555):
    """Reset ZMQ connection to clear any buffered data."""
    print("🔄 Resetting ZMQ connection to clear buffered data...")
    logging.info("Resetting ZMQ connection to clear buffered data...")

    try:
        import zmq
        # Terminate any existing context
        try:
            zmq.Context.instance().term()
            time.sleep(3)  # Longer wait for complete cleanup
            zmq._context_initialized = False
        except:
            pass

        # Force garbage collection
        import gc
        gc.collect()

        # Additional cleanup for more aggressive reset
        if hasattr(zmq.Context, '_instance'):
            zmq.Context._instance = None

        print("✅ ZMQ connection reset completed - all buffered data cleared")
        logging.info("ZMQ connection reset completed - all buffered data cleared")
        return True

    except Exception as e:
        print(f"❌ Error during ZMQ reset: {e}")
        logging.error(f"Error during ZMQ reset: {e}")
        return False

def main():
    """Run the best agent found by Optuna."""
    print("Starting best agent from Optuna optimization")
    print("NOTE: This agent will ONLY use real data from OMNeT++ and will pause when no data is available")
    print("      No synthetic data will be used during training or evaluation")
    print("      Loss explosion detection and learning rate decay are ENABLED")
    print("      If loss explodes continuously, you'll be prompted to save and stop or reset and continue")
    logging.info("Starting best agent from Optuna optimization")
    logging.info("NOTE: This agent will ONLY use real data from OMNeT++ and will pause when no data is available")
    logging.info("      No synthetic data will be used during training or evaluation")
    logging.info("      Loss explosion detection and learning rate decay are ENABLED")
    logging.info("      If loss explodes continuously, user will be prompted to save and stop or reset and continue")

    # Load the best parameters
    best_params_file = input("Enter the path to the best parameters file (default: optuna_results_improved.json): ") or "optuna_results_improved.json"
    best_params, metadata = load_best_params(best_params_file)

    if best_params is None:
        return 1

    print(f"Loaded best parameters from {best_params_file}:")
    if metadata:
        print(f"  Trial #{metadata['trial_number']} with value {metadata['best_value']}")
        print(f"  Selected from {metadata['total_trials']} total trials")

    print("  Parameters:")
    for key, value in best_params.items():
        print(f"    {key}: {value}")

    # Reset ZMQ connection to clear any buffered data
    if not reset_zmq_connection():
        print("Warning: ZMQ reset failed, but continuing anyway...")
        logging.warning("ZMQ reset failed, but continuing anyway...")

    # Create environment with robust error handling
    max_attempts = 5
    env = None

    for attempt in range(max_attempts):
        try:
            print(f"Connection attempt {attempt+1}/{max_attempts}")
            logging.info(f"Connection attempt {attempt+1}/{max_attempts}")

            # Create environment
            env = TSNGCLEnvironment(port=5555)

            print(f"Connection attempt {attempt+1} successful")
            print("Environment initialized - waiting for real data from OMNeT++")
            print("No synthetic data will be used - agent will pause if no data is available")
            logging.info(f"Connection attempt {attempt+1} successful")
            logging.info("Environment initialized - waiting for real data from OMNeT++")
            logging.info("No synthetic data will be used - agent will pause if no data is available")

            # Wait for socket to initialize
            time.sleep(2)
            break
        except Exception as e:
            print(f"Error during connection attempt {attempt+1}: {e}")
            logging.error(f"Error during connection attempt {attempt+1}: {e}")
            if env:
                env.close()
            env = None
            time.sleep(5)

    if env is None:
        print("Failed to create environment after multiple attempts")
        logging.error("Failed to create environment after multiple attempts")
        return 1

    try:
        # Prepare parameters for agent creation
        learning_rate = best_params.get('learning_rate', 1e-5)  # Reduced from 5e-5 to 1e-5

        # Create fc_layer_params based on n_layers and layer_size
        n_layers = best_params.get('n_layers', 2)
        layer_size = best_params.get('layer_size', 64)
        fc_layer_params = tuple([layer_size] * n_layers)

        # Map activation function
        activation_name = best_params.get('activation_fn', 'relu')
        activation_fn_map = {
            'relu': tf.keras.activations.relu,
            'elu': tf.keras.activations.elu,
            'tanh': tf.keras.activations.tanh,
            'swish': tf.nn.swish
        }
        activation_fn = activation_fn_map.get(activation_name, tf.keras.activations.relu)

        # Create the agent with best parameters from improved Optuna
        agent, tf_env, lr_scheduler = create_ppo_agent(
            env,
            learning_rate=learning_rate,
            fc_layer_params=fc_layer_params,
            activation_fn=activation_fn,
            entropy_regularization=best_params.get('entropy_regularization', 0.01),
            importance_ratio_clipping=best_params.get('importance_ratio_clipping', 0.2),
            num_epochs=best_params.get('num_epochs', 3),
            lambda_value=best_params.get('lambda_value', 0.97),
            discount_factor=best_params.get('discount_factor', 0.995)
        )

        # Create enhanced trainer with loss explosion detection
        trainer = LossAwareTrainer(
            env=env,
            agent=agent,
            tf_env=tf_env,
            num_iterations=best_params.get('num_iterations', 20),
            collect_steps_per_iteration=best_params.get('collect_steps_per_iteration', 100),
            lr_scheduler=lr_scheduler
        )

        # Set up model saving callback for loss explosion handling
        def create_save_callback(best_params, metadata):
            def save_callback(iteration, returns):
                return save_model(agent, iteration=iteration,
                                best_params=best_params,
                                returns=returns,
                                metadata=metadata)
            return save_callback

        trainer.set_save_callback(create_save_callback(best_params, metadata))

        # Set early stopping parameters
        trainer.patience = 10  # More patience for better convergence
        trainer.min_delta_loss = 0.005  # Smaller delta for more precise convergence
        trainer.min_delta_return = 0.05  # Smaller delta for more precise convergence

        print("Starting training with best parameters...")
        print("Training will only use real data from OMNeT++ - agent will pause if no data is available")
        print("🔥 Loss explosion detection is ACTIVE - learning rate will decay if loss increases")
        print("📊 If loss explodes repeatedly, you'll be prompted to save/stop or reset and continue")
        logging.info("Starting training with best parameters...")
        logging.info("Training will only use real data from OMNeT++ - agent will pause if no data is available")
        logging.info("Loss explosion detection is ACTIVE - learning rate will decay if loss increases")
        logging.info("If loss explodes repeatedly, user will be prompted to save/stop or reset and continue")

        # Add a data activity check before starting training
        print("Checking for data activity...")
        data_check_start = time.time()
        data_check_timeout = 30  # seconds
        while not env.data_active and time.time() - data_check_start < data_check_timeout:
            print("Waiting for data from OMNeT++... (press Ctrl+C to skip check)")
            time.sleep(2)
            # The environment automatically monitors data activity in a background thread

        if env.data_active:
            print("Data is active! Starting training.")
            logging.info("Data is active! Starting training.")
        else:
            print("No data detected, but continuing anyway. Training will wait for data.")
            logging.warning("No data detected, but continuing anyway. Training will wait for data.")
            print(f"Environment data_active status: {env.data_active}")
            print(f"Last data received time: {env.last_data_received_time}")
            logging.warning(f"Environment data_active status: {env.data_active}")
            logging.warning(f"Last data received time: {env.last_data_received_time}")

        # Run the training loop with progress monitoring
        print("\n" + "="*50)
        print("STARTING TRAINING LOOP")
        print("="*50)

        # Add a monitoring thread to show that the program is still running
        def monitor_data_activity():
            last_report = time.time()
            while True:
                current_time = time.time()
                if current_time - last_report > 10:  # Report every 10 seconds
                    if env.last_data_received_time is not None:
                        time_since_last = time.time() - env.last_data_received_time
                        print(f"[Monitor] Data active: {env.data_active}, Last data received: {time_since_last:.1f}s ago")
                    else:
                        print(f"[Monitor] Data active: {env.data_active}, No data received yet")
                    last_report = current_time
                time.sleep(1)

        import threading
        monitor_thread = threading.Thread(target=monitor_data_activity, daemon=True)
        monitor_thread.start()

        # Display learning rate scheduler information
        if lr_scheduler:
            print(f"📊 Learning Rate Scheduler Configuration:")
            print(f"   Initial LR: {lr_scheduler.initial_learning_rate:.2e}")
            print(f"   Decay Rate: {lr_scheduler.decay_rate}")
            print(f"   Loss Threshold: {lr_scheduler.loss_increase_threshold}")
            print(f"   History Size: {lr_scheduler.history_size}")
            print(f"   Explosion Limit: {lr_scheduler.consecutive_explosions_limit}")
            logging.info(f"Learning Rate Scheduler - Initial LR: {lr_scheduler.initial_learning_rate:.2e}, "
                        f"Decay Rate: {lr_scheduler.decay_rate}, Threshold: {lr_scheduler.loss_increase_threshold}")

        # Run the training loop
        print("About to start training loop...")
        logging.info("About to start training loop...")

        # Collect detailed metrics during training
        start_time = time.time()
        returns = trainer.run_training()
        end_time = time.time()

        # Check if training was stopped due to loss explosion
        if isinstance(returns, str) and returns == "SAVE_AND_STOP":
            print("🛑 Training stopped due to loss explosion - model has been saved")
            logging.info("Training stopped due to loss explosion - model has been saved")
            return 0  # Exit successfully since model was saved
        elif returns is None or len(returns) == 0:
            print("⚠️ Training completed but no returns were recorded")
            logging.warning("Training completed but no returns were recorded")
            returns = []
        else:
            print(f"✅ Training loop completed successfully. Returns: {returns}")
            logging.info(f"Training loop completed successfully. Returns: {returns}")

        # Collect detailed metrics
        detailed_metrics = {
            'training_duration_seconds': end_time - start_time,
            'training_start_time': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time)),
            'training_end_time': time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time)),
            'returns': returns if isinstance(returns, list) else [],
            'final_return': returns[-1] if returns and len(returns) > 0 else None,
            'best_return': max(returns) if returns and len(returns) > 0 else None,
            'worst_return': min(returns) if returns and len(returns) > 0 else None,
            'mean_return': sum(returns) / len(returns) if returns and len(returns) > 0 else None,
            'return_std': np.std(returns) if returns and len(returns) > 0 else None,
            'total_iterations': len(returns) if returns and isinstance(returns, list) else 0,
            'hyperparameters_used': best_params.copy(),
            'optuna_metadata': metadata.copy() if metadata else None,
            'loss_explosion_occurred': isinstance(returns, str) and returns == "SAVE_AND_STOP",
            'learning_rate_scheduler_used': True,
            'zmq_reset_performed': True
        }

        # Save the final model with detailed metrics
        final_model_path = save_model(agent, iteration="final",
                                    best_params=best_params,
                                    returns=returns,
                                    metadata=metadata,
                                    detailed_metrics=detailed_metrics)

        if final_model_path:
            print(f"Final model saved to {final_model_path}")
            logging.info(f"Final model saved to {final_model_path}")

        # Print detailed training results
        print(f"\n{'='*70}")
        print("TRAINING RESULTS SUMMARY")
        print(f"{'='*70}")
        print(f"Training Duration: {detailed_metrics['training_duration_seconds']:.2f} seconds")
        print(f"Total Iterations: {detailed_metrics['total_iterations']}")
        print(f"ZMQ Reset Performed: {'✅ Yes' if detailed_metrics['zmq_reset_performed'] else '❌ No'}")
        print(f"Loss Explosion Detection: {'✅ Active' if detailed_metrics['learning_rate_scheduler_used'] else '❌ Inactive'}")
        print(f"Loss Explosion Occurred: {'🔥 Yes' if detailed_metrics['loss_explosion_occurred'] else '✅ No'}")

        if returns and len(returns) > 0 and isinstance(returns, list):
            print(f"Final Return: {detailed_metrics['final_return']:.4f}")
            print(f"Best Return: {detailed_metrics['best_return']:.4f}")
            print(f"Worst Return: {detailed_metrics['worst_return']:.4f}")
            print(f"Mean Return: {detailed_metrics['mean_return']:.4f}")
            print(f"Return Std Dev: {detailed_metrics['return_std']:.4f}")

            logging.info(f"Training completed with final return: {returns[-1]}")
            logging.info(f"Best return during training: {max(returns)}")
        else:
            print("No return data available (training may have been stopped early)")
            logging.warning("No return data available (training may have been stopped early)")

        if metadata:
            print(f"Optuna Trial: #{metadata['trial_number']} (value: {metadata['best_value']})")

        print(f"Training duration: {detailed_metrics['training_duration_seconds']:.2f} seconds")
        print(f"{'='*70}")

        logging.info(f"Training duration: {detailed_metrics['training_duration_seconds']:.2f} seconds")

        # Save detailed results to file
        with open("best_agent_detailed_results.json", "w") as f:
            json.dump(detailed_metrics, f, indent=4)

        print("Detailed results saved to best_agent_detailed_results.json")
        logging.info("Detailed results saved to best_agent_detailed_results.json")

        # Create comprehensive plots to assess model efficacy
        if returns and len(returns) > 0 and isinstance(returns, list):
            try:
                create_comprehensive_plots(returns, trainer, detailed_metrics, best_params)
            except Exception as e:
                print(f"Error creating comprehensive plots: {e}")
                logging.error(f"Error creating comprehensive plots: {e}")

    except KeyboardInterrupt:
        print("\nTraining interrupted by user.")
        logging.info("Training interrupted by user.")
    except Exception as e:
        print(f"Error during training: {e}")
        logging.error(f"Error during training: {e}")
        import traceback
        traceback.print_exc()
        logging.error(traceback.format_exc())
        return 1
    finally:
        # Make sure to close the environment
        if env:
            print("Closing environment...")
            logging.info("Closing environment...")
            env.close()
            print("Environment closed.")
            logging.info("Environment closed.")

        # Clean up multiprocessing resources
        if 'multiprocessing' in sys.modules:
            try:
                import multiprocessing.pool
                multiprocessing.pool.ThreadPool._wrap_exception = True
            except:
                pass

    return 0

if __name__ == "__main__":
    sys.exit(main())
